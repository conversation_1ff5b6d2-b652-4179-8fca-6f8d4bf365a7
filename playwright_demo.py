import re
import time

from patchright.async_api import Page
from playwright.sync_api import sync_playwright
import requests


def main():
    print("Hello from playwtight-demo!")


async def click_visible_elements_by_text(page: Page, pattern: str, click_all: bool = False) -> bool:
    """
    定位并点击可见元素的通用函数

    Args:
        page: 浏览器页面对象
        pattern: 正则表达式模式字符串
    """
    locator = page.get_by_text(re.compile(pattern))
    elements = await locator.all()
    for ele in elements:
        if await ele.is_visible():
            await ele.click()
            if not click_all:
                return True
    return False


with sync_playwright() as p:
    cdp_url = "http://localhost:14651"
    # 获取可用页面
    json_list = requests.get(cdp_url + "/json").json()
    print("可用页面：" + str(len(json_list)))
    for i, target in enumerate(json_list):
        print(f"[{i}] {target['title']} -> {target['url']}")

    target_ws = json_list[0]["webSocketDebuggerUrl"]  # 🔗 指向某个页面的 WebSocket
    print("WebSocket URL:", target_ws, "\n")

    # 如果你想连接第 0 个页面
    headers = {"User-Agent": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8"}
    browser = p.chromium.connect_over_cdp(cdp_url)

    context = browser.contexts[0]
    page = context.pages[0]

    # page.goto(
    #     "https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html?_u=24&PrdTyp=A&behavior_entryid=LC0JGQ001&behavior_pageid=459C25E0&behavior_slotid=&appflag=1&_urlrefer=PLATFORM&DeviceType=E&Version=13.3.3&SystemVersion=11",
    #     wait_until="networkidle")

    print("当前标题：", page.title())
    print("当前URL：", page.url)

    with open("list.html", "w", encoding="utf-8") as f:
        f.write(page.content())

    page.evaluate("""
    ['body', 'html', '#app', '#root', '#main', '.swiper-container', '.sa-list', '.swiper-wrapper', ].forEach(sel => {
      const el = document.querySelector(sel);
      if (el) el.style.overflow = 'auto';
    });
    """)


    while True:
        print("scrollTo")

        page.evaluate("window.scrollTo(0, -300)")
        time.sleep(1)
        page.evaluate("window.scrollTo(0, 300)")
        time.sleep(1)

        # page.mouse.wheel(0, -300)
        # page.wait_for_timeout(500)  # 等待加载
        # page.mouse.wheel(0, 300)
        # page.wait_for_timeout(500)  # 等待加载


    # ele = page.locator('#scroll-content-cart-essential')
    # print(ele.count())
    # plus = ele.get_by_label("加购")
    # print(plus.count())
    # plus.locator('.btn_plus').click()
    # p = plus.locator('div[id^="btn__plus-"]')
    # p=plus.locator('> div')
    # p = ele.locator('div[aria-label="加购"] > div:first-child')
    # p = page.locator('div[aria-label="加购"] > div').first.click()
    # print(p.count())
    # p.nth(0).click()

# if __name__ == "__main__":
#     main()
