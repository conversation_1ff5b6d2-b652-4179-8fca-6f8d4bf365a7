"""
示例：如何添加新的表格数据类型
演示如何通过配置快速添加新的chart_api支持
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any

# 导入必要的模块
from src.nm_crawl.config.table_config import (
    TableConfig, FieldMapping, register_table_config,
    convert_to_float, validate_date_format
)
from src.nm_crawl.models.financial_models import HistoryPerformance
from src.nm_crawl.services.database_service import CmbDatabaseService
from src.nm_crawl.database.connection import init_database


def create_performance_config():
    """创建历史业绩表格配置"""
    
    # 定义字段映射
    field_mappings = [
        FieldMapping("date", "performance_date", required=True),
        FieldMapping("annualReturn", "annual_return", transformer=convert_to_float),
        FieldMapping("cumulativeReturn", "cumulative_return", transformer=convert_to_float),
        FieldMapping("volatility", "volatility", transformer=convert_to_float),
        FieldMapping("maxDrawdown", "max_drawdown", transformer=convert_to_float),
        FieldMapping("sharpeRatio", "sharpe_ratio", transformer=convert_to_float),
    ]
    
    # 自定义数据验证函数
    def validate_performance_data(data: Dict[str, Any]) -> bool:
        """验证业绩数据"""
        # 检查必填字段
        if not data.get('date'):
            return False
        
        # 验证日期格式
        if not validate_date_format(data['date']):
            return False
        
        # 检查至少有一个业绩指标
        performance_fields = ['annualReturn', 'cumulativeReturn', 'volatility', 'maxDrawdown', 'sharpeRatio']
        has_performance_data = any(data.get(field) for field in performance_fields)
        
        return has_performance_data
    
    # 创建表格配置
    performance_config = TableConfig(
        api_name="get-history-performance",  # 新的API名称
        table_name="history_performance",
        model_class=HistoryPerformance,
        date_field="performance_date",
        field_mappings=field_mappings,
        validator=validate_performance_data
    )
    
    return performance_config


async def test_new_table_config():
    """测试新的表格配置"""
    
    print("=== 添加新表格配置示例 ===")
    
    # 1. 创建并注册新的表格配置
    performance_config = create_performance_config()
    register_table_config(performance_config)
    print(f"✓ 注册新的表格配置: {performance_config.api_name}")
    
    # 2. 初始化数据库
    await init_database()
    print("✓ 数据库初始化完成")
    
    # 3. 创建数据库服务
    db_service = CmbDatabaseService()
    
    # 4. 检查API是否支持
    is_supported = await db_service.is_chart_api_supported("get-history-performance")
    print(f"✓ API支持检查: {is_supported}")
    
    # 5. 准备测试数据
    test_data = [
        {
            "date": "2024-01-01",
            "annualReturn": "5.25",
            "cumulativeReturn": "12.50",
            "volatility": "2.15",
            "maxDrawdown": "-1.25",
            "sharpeRatio": "1.85"
        },
        {
            "date": "2024-01-02",
            "annualReturn": "5.30",
            "cumulativeReturn": "12.55",
            "volatility": "2.18",
            "maxDrawdown": "-1.30",
            "sharpeRatio": "1.82"
        },
        {
            "date": "2024-01-03",
            "annualReturn": "5.28",
            "cumulativeReturn": "12.52",
            "volatility": "2.20",
            "maxDrawdown": "-1.35",
            "sharpeRatio": "1.80"
        }
    ]
    
    # 6. 保存测试数据
    rip_cod = "TEST001"
    crawl_time = datetime.now()
    
    try:
        saved_count = await db_service.save_chart_data(
            "get-history-performance", 
            rip_cod, 
            test_data, 
            crawl_time
        )
        print(f"✓ 成功保存 {saved_count} 条业绩记录")
        
    except Exception as e:
        print(f"✗ 保存数据失败: {e}")
        return
    
    # 7. 验证数据保存
    from sqlalchemy import select
    from src.nm_crawl.database.connection import get_db_session
    
    async for session in get_db_session():
        stmt = select(HistoryPerformance).where(
            HistoryPerformance.rip_cod == rip_cod
        ).order_by(HistoryPerformance.performance_date)
        
        result = await session.execute(stmt)
        records = result.scalars().all()
        
        print(f"✓ 数据库中找到 {len(records)} 条记录")
        
        for record in records:
            print(f"  - {record.performance_date}: 年化收益 {record.annual_return}%, "
                  f"累计收益 {record.cumulative_return}%, 夏普比率 {record.sharpe_ratio}")
        
        break
    
    print("\n=== 配置完成 ===")
    print("现在您可以在爬虫中使用 'get-history-performance' API了！")
    print("只需要在 chart_apis 列表中添加这个API名称即可。")


def show_usage_example():
    """展示使用示例"""
    
    print("\n=== 使用方法 ===")
    print("1. 在爬虫配置中添加新的API:")
    print("""
    self.chart_apis = [
        "get-history-profit",      # 万份收益
        "get-history-net-value",   # 单位净值
        "get-history-performance", # 历史业绩 (新增)
    ]
    """)
    
    print("2. 爬虫会自动识别并处理新的API数据")
    print("3. 数据会根据配置自动保存到对应的数据库表")
    
    print("\n=== 添加新表格的步骤 ===")
    print("1. 在 financial_models.py 中定义新的数据库表模型")
    print("2. 创建 TableConfig 配置，定义字段映射和验证规则")
    print("3. 注册配置到 table_config_manager")
    print("4. 在爬虫的 chart_apis 中添加新的API名称")
    print("5. 运行爬虫，数据会自动保存到新表中")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_new_table_config())
    
    # 显示使用示例
    show_usage_example()
