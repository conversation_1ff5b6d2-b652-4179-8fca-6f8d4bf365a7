#!/usr/bin/env python3
"""
API-based crawler that tries to find and call the actual API endpoints
"""

import asyncio
import json
import sys
import os
import re
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import httpx
from loguru import logger


async def analyze_page_for_apis():
    """分析页面找到API端点"""
    
    list_page_url = (
        "https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html"
        "?_u=24&PrdTyp=A&behavior_entryid=LC0JGQ001&behavior_pageid=459C25E0"
        "&behavior_slotid=&appflag=1&_urlrefer=PLATFORM&DeviceType=E"
        "&Version=13.3.3&SystemVersion=11"
    )
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }
    
    logger.info("分析页面源码...")
    
    try:
        async with httpx.AsyncClient(timeout=30) as client:
            response = await client.get(list_page_url, headers=headers)
            
            if response.status_code == 200:
                html_content = response.text
                logger.info(f"页面加载成功，长度: {len(html_content)}")
                
                # 保存HTML用于分析
                os.makedirs("debug", exist_ok=True)
                with open("debug/page_source.html", 'w', encoding='utf-8') as f:
                    f.write(html_content)
                
                # 查找可能的API端点
                api_patterns = [
                    r'https?://[^"\']+/ientrustfinance/[^"\']+',
                    r'https?://[^"\']+/financelist/[^"\']+',
                    r'https?://[^"\']+cashlistnew[^"\']*',
                    r'/ientrustfinance/[^"\']+',
                    r'financelist/[^"\']+',
                    r'cashlistnew[^"\']*'
                ]
                
                found_apis = set()
                for pattern in api_patterns:
                    matches = re.findall(pattern, html_content, re.IGNORECASE)
                    found_apis.update(matches)
                
                logger.info(f"找到 {len(found_apis)} 个可能的API端点:")
                for api in sorted(found_apis):
                    logger.info(f"  - {api}")
                
                # 查找JavaScript中的配置信息
                js_patterns = [
                    r'PageInfoModel\s*=\s*({[^}]+})',
                    r'var\s+\w+\s*=\s*["\']https?://[^"\']+["\']',
                    r'baseURL["\']?\s*:\s*["\']([^"\']+)["\']',
                    r'apiUrl["\']?\s*:\s*["\']([^"\']+)["\']'
                ]
                
                for pattern in js_patterns:
                    matches = re.findall(pattern, html_content, re.IGNORECASE)
                    if matches:
                        logger.info(f"找到配置: {matches}")
                
                return found_apis, html_content
            else:
                logger.error(f"页面加载失败: {response.status_code}")
                return set(), ""
                
    except Exception as e:
        logger.error(f"页面分析失败: {e}")
        return set(), ""


async def try_api_endpoints():
    """尝试调用可能的API端点"""
    
    # 基于分析和常见模式的API端点
    possible_apis = [
        "https://mobile.cmbchina.com/ientrustfinance/financelist/cashlistnew",
        "https://mobile.cmbchina.com/ientrustfinance/financelist/cashlist",
        "https://mobile.cmbchina.com/ientrustfinance/product/list",
        "https://mobile.cmbchina.com/ientrustfinance/api/financelist",
        "https://mobile.cmbchina.com/api/ientrustfinance/financelist/cashlistnew"
    ]
    
    # 不同的请求参数组合
    param_combinations = [
        {},
        {"PrdTyp": "A"},
        {"PrdTyp": "A", "pageSize": "20", "pageNum": "1"},
        {"type": "A", "page": "1", "size": "20"},
        {"category": "A", "offset": "0", "limit": "20"}
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Referer': 'https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html',
        'X-Requested-With': 'XMLHttpRequest'
    }
    
    logger.info("尝试调用API端点...")
    
    async with httpx.AsyncClient(timeout=30) as client:
        for api_url in possible_apis:
            for params in param_combinations:
                try:
                    logger.info(f"尝试: {api_url} with params: {params}")
                    
                    # GET请求
                    response = await client.get(api_url, params=params, headers=headers)
                    
                    logger.info(f"  GET响应: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            logger.info(f"  ✓ 成功获取JSON数据")
                            
                            # 保存响应数据
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            filename = f"debug/api_response_{timestamp}.json"
                            with open(filename, 'w', encoding='utf-8') as f:
                                json.dump(data, f, ensure_ascii=False, indent=2)
                            
                            # 分析数据结构
                            products = analyze_api_response(data)
                            if products:
                                logger.info(f"  ✓ 找到 {len(products)} 个产品!")
                                return products, api_url, params
                            
                        except json.JSONDecodeError:
                            logger.info(f"  响应不是JSON格式: {response.text[:200]}...")
                    
                    # 如果GET失败，尝试POST
                    if response.status_code != 200:
                        post_response = await client.post(api_url, json=params, headers=headers)
                        logger.info(f"  POST响应: {post_response.status_code}")
                        
                        if post_response.status_code == 200:
                            try:
                                data = post_response.json()
                                products = analyze_api_response(data)
                                if products:
                                    logger.info(f"  ✓ POST成功找到 {len(products)} 个产品!")
                                    return products, api_url, params
                            except json.JSONDecodeError:
                                pass
                    
                except Exception as e:
                    logger.debug(f"  请求失败: {e}")
                    continue
    
    return [], None, None


def analyze_api_response(data):
    """分析API响应数据"""
    products = []
    
    try:
        # 常见的数据结构模式
        possible_paths = [
            ["data", "prdList"],
            ["bizResult", "data", "prdList"],
            ["result", "data"],
            ["data", "list"],
            ["data", "items"],
            ["list"],
            ["items"],
            ["products"],
            ["data"]
        ]
        
        for path in possible_paths:
            current = data
            try:
                for key in path:
                    current = current[key]
                
                if isinstance(current, list) and len(current) > 0:
                    # 检查是否看起来像产品数据
                    first_item = current[0]
                    if isinstance(first_item, dict):
                        # 查找产品相关字段
                        product_fields = ['ripSnm', 'ripCod', 'prdRat', 'name', 'title', 'rate']
                        if any(field in first_item for field in product_fields):
                            logger.info(f"  在路径 {' -> '.join(path)} 找到产品数据")
                            return current
                            
            except (KeyError, TypeError, IndexError):
                continue
        
        # 如果没有找到标准结构，尝试递归查找
        products = find_products_recursive(data)
        
    except Exception as e:
        logger.error(f"分析API响应失败: {e}")
    
    return products


def find_products_recursive(obj, depth=0, max_depth=5):
    """递归查找产品数据"""
    if depth > max_depth:
        return []
    
    if isinstance(obj, list):
        if len(obj) > 0 and isinstance(obj[0], dict):
            # 检查是否是产品列表
            first_item = obj[0]
            product_indicators = ['ripSnm', 'ripCod', 'prdRat', 'name', 'title', 'rate', '理财', '收益']
            
            if any(key in first_item or (isinstance(key, str) and key in str(first_item)) 
                   for key in product_indicators):
                return obj
        
        # 递归检查列表中的每个元素
        for item in obj:
            result = find_products_recursive(item, depth + 1, max_depth)
            if result:
                return result
    
    elif isinstance(obj, dict):
        # 递归检查字典中的每个值
        for value in obj.values():
            result = find_products_recursive(value, depth + 1, max_depth)
            if result:
                return result
    
    return []


async def main():
    """主函数"""
    logger.info("开始API爬虫测试")
    
    try:
        # 第一步：分析页面
        found_apis, html_content = await analyze_page_for_apis()
        
        # 第二步：尝试API调用
        products, successful_api, successful_params = await try_api_endpoints()
        
        if products:
            logger.info(f"✓ 成功获取到 {len(products)} 个产品")
            logger.info(f"成功的API: {successful_api}")
            logger.info(f"成功的参数: {successful_params}")
            
            # 显示前几个产品
            for i, product in enumerate(products[:5], 1):
                name = product.get('ripSnm') or product.get('name') or product.get('title', '未知产品')
                rate = product.get('prdRat') or product.get('rate', 'N/A')
                logger.info(f"  {i}. {name} - {rate}")
                
            # 保存产品数据
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            products_file = f"debug/products_{timestamp}.json"
            with open(products_file, 'w', encoding='utf-8') as f:
                json.dump(products, f, ensure_ascii=False, indent=2)
            logger.info(f"产品数据已保存到: {products_file}")
            
        else:
            logger.warning("✗ 没有找到任何产品数据")
            
            print("\n可能的原因:")
            print("1. API需要特殊的认证或token")
            print("2. API参数不正确")
            print("3. 需要先访问页面获取session")
            print("4. API地址已经改变")
            print("5. 需要模拟完整的浏览器行为")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 设置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:HH:mm:ss} | {level} | {message}")
    
    # 运行测试
    asyncio.run(main())
