# 通用表格数据处理框架

## 概述

本框架提供了一个通用的解决方案，用于快速添加和处理不同类型的理财产品表格数据。通过配置化的方式，您可以轻松地添加新的 chart_api 类型和对应的数据库表，而无需修改核心业务逻辑。

## 核心组件

### 1. 表格配置管理器 (`table_config.py`)

负责管理所有表格的配置信息，包括：
- API名称到表结构的映射
- 字段映射规则
- 数据转换函数
- 数据验证规则

### 2. 通用表格服务 (`generic_table_service.py`)

提供统一的数据处理接口：
- 根据配置动态处理不同类型的数据
- 自动进行字段映射和数据转换
- 统一的数据验证和保存逻辑

### 3. 数据库服务扩展 (`database_service.py`)

集成通用表格服务，提供：
- `save_chart_data()` - 通用的图表数据保存方法
- `get_supported_chart_apis()` - 获取支持的API列表
- `is_chart_api_supported()` - 检查API是否支持

## 现有支持的表格类型

### 1. 历史收益数据 (`get-history-profit`)
- 表名: `history_profits`
- 字段: 万份收益、七日年化收益率
- 日期字段: `profit_date`

### 2. 历史净值数据 (`get-history-net-value`)
- 表名: `history_netvalue`
- 字段: 单位净值、累计净值、净值变化
- 日期字段: `net_value_date`

## 如何添加新的表格类型

### 步骤1: 定义数据库表模型

在 `src/nm_crawl/models/financial_models.py` 中添加新的表模型：

```python
class HistoryPerformance(Base):
    """理财产品历史业绩表"""
    __tablename__ = "history_performance"

    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(String(100), nullable=False, index=True)
    rip_cod = Column(String(50), nullable=False, index=True)
    
    # 业绩数据字段
    performance_date = Column(String(20), nullable=False)
    annual_return = Column(String(20))
    cumulative_return = Column(String(20))
    volatility = Column(String(20))
    
    # 其他标准字段
    raw_data = Column(JSON)
    created_at = Column(DateTime, default=func.now())
    crawl_time = Column(DateTime)
    
    __table_args__ = (
        Index('idx_performance_product_date', 'product_id', 'performance_date', unique=True),
    )
```

### 步骤2: 创建表格配置

```python
from src.nm_crawl.config.table_config import TableConfig, FieldMapping, register_table_config

# 定义字段映射
field_mappings = [
    FieldMapping("date", "performance_date", required=True),
    FieldMapping("annualReturn", "annual_return", transformer=convert_to_float),
    FieldMapping("cumulativeReturn", "cumulative_return", transformer=convert_to_float),
    # ... 更多字段映射
]

# 创建配置
performance_config = TableConfig(
    api_name="get-history-performance",
    table_name="history_performance",
    model_class=HistoryPerformance,
    date_field="performance_date",
    field_mappings=field_mappings,
    validator=custom_validator_function  # 可选
)

# 注册配置
register_table_config(performance_config)
```

### 步骤3: 在爬虫中添加API

在 `detail_crawler.py` 中的 `chart_apis` 列表中添加新的API：

```python
self.chart_apis = [
    "get-history-profit",      # 万份收益
    "get-history-net-value",   # 单位净值
    "get-history-performance", # 历史业绩 (新增)
]
```

### 步骤4: 运行爬虫

爬虫会自动识别新的API，并根据配置处理数据：

```python
# 爬虫会自动调用
await self.db_service.save_chart_data(chart_name, rip_cod, chart_data, crawl_time)
```

## 配置选项详解

### FieldMapping 参数

- `source_field`: 源数据中的字段名
- `target_field`: 目标表中的字段名
- `data_type`: 数据类型 ("string", "integer", "float", "boolean", "json")
- `required`: 是否必填字段
- `default_value`: 默认值
- `transformer`: 自定义转换函数

### TableConfig 参数

- `api_name`: chart_api名称，用于匹配URL
- `table_name`: 数据库表名
- `model_class`: SQLAlchemy模型类
- `date_field`: 日期字段名（用于去重）
- `field_mappings`: 字段映射列表
- `data_extractor`: 自定义数据提取函数（可选）
- `validator`: 数据验证函数（可选）

## 内置转换函数

框架提供了常用的数据转换函数：

- `convert_to_boolean()`: 转换为布尔值
- `convert_to_float()`: 转换为浮点数
- `convert_to_int()`: 转换为整数

## 内置验证函数

- `validate_date_format()`: 验证日期格式
- `validate_profit_data()`: 验证收益数据
- `validate_netvalue_data()`: 验证净值数据

## 使用示例

参考 `examples/add_new_table_example.py` 文件，其中包含了完整的示例代码，展示如何：

1. 创建新的表格配置
2. 注册配置
3. 测试数据保存
4. 验证数据完整性

## 最佳实践

1. **字段命名**: 使用清晰、一致的字段命名规范
2. **数据验证**: 为每个表格类型定义适当的验证规则
3. **错误处理**: 在转换函数中添加适当的错误处理
4. **索引优化**: 为经常查询的字段添加数据库索引
5. **文档更新**: 添加新表格时更新相关文档

## 故障排除

### 常见问题

1. **配置未生效**: 确保配置已正确注册到 `table_config_manager`
2. **字段映射错误**: 检查源数据字段名是否与配置中的 `source_field` 匹配
3. **数据类型转换失败**: 检查转换函数是否正确处理了异常情况
4. **数据库约束冲突**: 确保唯一索引字段的组合是正确的

### 调试技巧

1. 启用详细日志记录
2. 使用示例数据进行测试
3. 检查数据库表结构是否正确创建
4. 验证API名称是否在爬虫的 `chart_apis` 列表中

## 扩展性

框架设计具有良好的扩展性：

- 支持自定义数据提取逻辑
- 支持复杂的数据转换规则
- 支持多种数据验证策略
- 支持动态配置加载

通过这个框架，您可以快速添加新的表格类型，而无需修改核心爬虫逻辑，大大提高了开发效率和代码的可维护性。
