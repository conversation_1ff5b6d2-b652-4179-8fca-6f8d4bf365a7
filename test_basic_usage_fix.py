#!/usr/bin/env python3
"""
Test the fixed basic usage example
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.main import CrawlerApp
from src.nm_crawl.utils.logger import setup_logging
from loguru import logger


async def test_basic_usage_fix():
    """测试修复后的基本用法"""
    
    logger.info("开始测试修复后的基本用法...")
    
    # 初始化应用
    app = CrawlerApp("config.json")
    await app.initialize()
    
    try:
        logger.info("开始爬取产品列表...")
        logger.info("注意：浏览器窗口会打开，请查看控制台日志（F12 -> Console）")
        
        # 爬取产品列表，使用较短的等待时间
        result = await app.crawl_list(wait_time=30, max_scroll_attempts=2)
        
        logger.info(f"✅ 爬取完成！获得 {result['products_count']} 个产品")
        
        # 显示前几个产品信息
        if result['products']:
            logger.info("前3个产品示例:")
            products = result['products'][:3]
            for i, product in enumerate(products, 1):
                logger.info(f"产品 {i}: {product.get('ripSnm', 'Unknown')} - {product.get('ripCod', 'Unknown')} - {product.get('prdRat', 'Unknown')}")
        else:
            logger.warning("没有获取到产品数据")
        
        return len(result['products']) > 0
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        await app.cleanup()


async def main():
    """主函数"""
    logger.info("=== 基本用法修复测试 ===")
    
    # 设置日志
    setup_logging(level="INFO")
    
    success = await test_basic_usage_fix()
    
    if success:
        logger.info("✅ 基本用法测试成功！")
        logger.info("现在你可以运行 python examples/basic_usage.py")
    else:
        logger.error("❌ 基本用法测试失败")
    
    return success


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
