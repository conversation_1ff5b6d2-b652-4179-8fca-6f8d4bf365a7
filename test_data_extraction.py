"""
测试数据提取逻辑
验证不同API响应的数据提取是否正确
"""

import json
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.crawlers.detail_crawler import FinancialProductDetailCrawler


async def test_data_extraction():
    """测试数据提取功能"""
    
    # 创建爬虫实例
    crawler = FinancialProductDetailCrawler()
    
    print("=== 测试数据提取逻辑 ===")
    
    # 测试 get-history-profit 数据结构 (直接列表)
    profit_response = {
        "sysCode": 200,
        "sysMsg": "Success",
        "bizResult": {
            "code": 200,
            "data": [
                {"date": "2024-01-01", "tenThousandProfit": "1.23", "sevenDaysAnnualProfit": "4.56"},
                {"date": "2024-01-02", "tenThousandProfit": "1.24", "sevenDaysAnnualProfit": "4.57"}
            ]
        }
    }
    
    print("1. 测试 get-history-profit 数据提取:")
    profit_data = await crawler._extract_history_from_response(json.dumps(profit_response))
    if profit_data:
        print(f"   ✓ 成功提取 {len(profit_data)} 条收益记录")
        print(f"   示例数据: {profit_data[0]}")
    else:
        print("   ✗ 收益数据提取失败")
    
    # 测试 get-history-performance 数据结构 (在list字段中)
    performance_response = {
        "sysCode": 200,
        "sysMsg": "Success",
        "bizResult": {
            "code": 200,
            "data": {
                "list": [
                    {
                        "prfTyp": "A",
                        "timeInterval": "近1月",
                        "netValueChange": "0.23",
                        "yeaYld": "2.68"
                    },
                    {
                        "prfTyp": "B",
                        "timeInterval": "近3月",
                        "netValueChange": "0.75",
                        "yeaYld": "2.96"
                    }
                ],
                "yearList": [],
                "yldSwh": "Y",
                "zdfSwh": "Y",
                "nvcShw": "Y"
            }
        }
    }
    
    print("\n2. 测试 get-history-performance 数据提取:")
    performance_data = await crawler._extract_history_from_response(json.dumps(performance_response))
    if performance_data:
        print(f"   ✓ 成功提取 {len(performance_data)} 条业绩记录")
        print(f"   示例数据: {performance_data[0]}")
    else:
        print("   ✗ 业绩数据提取失败")
    
    # 测试无效数据
    print("\n3. 测试无效数据处理:")
    invalid_response = {"sysCode": 500, "sysMsg": "Error"}
    invalid_data = await crawler._extract_history_from_response(json.dumps(invalid_response))
    if invalid_data is None:
        print("   ✓ 正确处理了无效数据")
    else:
        print("   ✗ 无效数据处理异常")
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    asyncio.run(test_data_extraction())
