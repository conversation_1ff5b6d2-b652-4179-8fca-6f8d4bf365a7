# 目标
获取银行理财产品的信息，包括列表和详细信息，保存成json文件和写入数据库

# 实现方式

1. 通过craw4ai打开指定url的网站，监听页面发起的fetch/xhr请求，获取回包数据，保存成json文件，写入数据库。
2. 先获取列表数据，然后获取详情数据。
3. 列表页数据可以拼接成详情页的url，然后打开详情页url，获取详情数据。
4. 详情数据可以保存成json文件，写入数据库。
。


# 步骤
1. 打开理财产品列表页url， "https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html?_u=24&PrdTyp=A&behavior_entryid=LC0JGQ001&behavior_pageid=459C25E0&behavior_slotid=&appflag=1&_urlrefer=PLATFORM&DeviceType=E&Version=13.3.3&SystemVersion=11"
2. 监听页面发起的xhr请求，所有的xhr请求都保存成json文件，写入数据库。其中列表页请求的url为"https://mobile.cmbchina.com/ientrustfinance/financelist/cashlistnew", 回包数据为json格式，包含理财产品列表信息，期间上滑会有多个cashlistnew请求，都需要监听， 把prdList拼接组合起来。 示例如下：
```json
{
    "sysCode": 200,
    "sysMsg": "Success",
    "bizResult": {
        "code": 200,
        "data": {
            "prdList": [
                {
                    "prdRat": "2.99%",
                    "ratDes": "7日年化",
                    "ripSnm": "工银添利宝现金28",
                    "zylTag": "I 代销工银理财",
                    "ripCod": "GY030111",
                    "saaCod": "D07",
                    "newFlg": "Y",
                    "fndNbr": "GY030111",
                    "prdInf": "买入下个工作日享收益",
                    "prdTags": [
                        {
                            "tagType": "200007.YX",
                            "tagName": "近一周超万人浏览"
                        }
                    ],
                    "terDay": "赎回下个工作日到账",
                    "dxsTag": "",
                    "sellOut": "N",
                    "jjbTag": "",
                    "salTim": "",
                    "prfOpenTag": false
                },
                {
                    "prdRat": "2.99%",
                    "ratDes": "7日年化",
                    "ripSnm": "日日金57号A",
                    "zylTag": "I 代销招银理财",
                    "ripCod": "8997A",
                    "saaCod": "D07",
                    "newFlg": "Y",
                    "fndNbr": "8997A",
                    "prdInf": "买入下个工作日享收益",
                    "prdTags": [
                        {
                            "tagType": "200006.YX",
                            "tagName": "近一周热搜"
                        },
                        {
                            "tagType": "A",
                            "tagName": "7*24小时交易"
                        }
                    ],
                    "terDay": "赎回预估下个工作日上午到",
                    "dxsTag": "",
                    "sellOut": "N",
                    "jjbTag": "",
                    "salTim": "",
                    "prfOpenTag": false
                },
                {
                    "prdRat": "2.99%",
                    "ratDes": "7日年化",
                    "ripSnm": "天天增利72号E",
                    "zylTag": "I 代销民生理财",
                    "ripCod": "MS030112",
                    "saaCod": "D07",
                    "newFlg": "Y",
                    "fndNbr": "MS030112",
                    "prdInf": "买入下个工作日享收益",
                    "prdTags": [
                        {
                            "tagType": "200007.YX",
                            "tagName": "近一周超万人浏览"
                        }
                    ],
                    "terDay": "赎回下个工作日到账",
                    "dxsTag": "",
                    "sellOut": "N",
                    "jjbTag": "",
                    "salTim": "",
                    "prfOpenTag": false
                },
                {
                    "prdRat": "2.84%",
                    "ratDes": "7日年化",
                    "ripSnm": "阳光碧乐活197D",
                    "zylTag": "I 代销光大理财",
                    "ripCod": "GD030142",
                    "saaCod": "D07",
                    "newFlg": "Y",
                    "fndNbr": "GD030142",
                    "prdInf": "买入下个交易日享收益",
                    "prdTags": [
                        {
                            "tagType": "200039.LY",
                            "tagName": "稳利严选丨近1月每日收益前20%"
                        }
                    ],
                    "terDay": "赎回下个交易日到账",
                    "dxsTag": "",
                    "sellOut": "N",
                    "jjbTag": "",
                    "salTim": "",
                    "prfOpenTag": false
                }
            ],
            "yDalCod": "Y",
            "yRipCod": "8235A",
            "ySaaCod": "D07",
            "timTmp": "1755843423700"
        }
    }
}
```
3. 详情页的url拼接格式为："https://mobile.cmbchina.com/IEntrustFinance/subsidiaryproduct/financedetail.html?popup=false&XRIPINN=MS030112&XSAACOD=D07"其中XRIPINN就是列表页的ripCod，XSAACOD就是列表页的saaCod。 打开详情页后，点击"万份收益"。 同时监听页面发起的xhr请求，所有的xhr请求都保存成json文件，写入数据库。详情页的回包数据为json格式，包含理财产品详细信息。其中详情页需要监听2个url：
一个url是"https://mobile.cmbchina.com/ientrustfinance/sa-finance-detail/prd-info"这个是产品详情。
回包数据如下：
```json
{
    "sysCode": 200,
    "sysMsg": "Success",
    "bizResult": {
        "code": 200,
        "data": {
            "saaCod": "D07",
            "ripInn": "PA030144",
            "ripSnm": "平安理财天天成长3号84期A",
            "ripNbr": "PA030144",
            "tabShow": "N",
            "sellPoint": "",
            "crpNam": "平安理财有限责任公司",
            "crpDec": "平安理财由平安银行全资发起设立，于2020年8月在深圳正式开业，是国内第五家正式开业的股份制银行理财子公司，公司以打造“中国品类最全的开放式理财平台”为目标，坚持市场化、专业化、精细化的经营策略。",
            "crpCod": "9PA",
            "rateText": "1.80<span class=\"ef-rat18\">%</span>",
            "rateTextTop": "1.80<span class=\"ef-rat14\">%</span>",
            "rateName": "七日年化",
            "ratDsc": "F",
            "terDay": "赎回下个工作日到账",
            "terDayName": "投资期限",
            "lmtCyc": "",
            "riskLvl": "R1低风险",
            "sbsUqt": "0.01元起购",
            "investTyp": "现金理财",
            "features": [
                {
                    "zTsdTyp": "A",
                    "zTsdTag": "Y",
                    "zTsdTtl": "申赎灵活，活钱理财",
                    "zTsdCtx": "银行工作日0:00-17:00申购/赎回，T+1日计算收益/T+1日赎回到账"
                },
                {
                    "zTsdTyp": "A",
                    "zTsdTag": "Y",
                    "zTsdTtl": "策略稳健，风险可控",
                    "zTsdCtx": "产品评级R1低风险，主要投资于银行存款、同业存单、货币基金、国债、高评级债券等固收类资产"
                },
                {
                    "zTsdTyp": "A",
                    "zTsdTag": "Y",
                    "zTsdTtl": "每日计提，按日结转",
                    "zTsdCtx": "产品采用摊余成本法估值，收益每日计提并结转份额"
                }
            ],
            "buyTimeName": "销售时间",
            "buyTimeRule": "每日00:00-23:59可购买，银行工作日17:00前购买视为当日申请",
            "buyFeeRule": "不收费",
            "confirmTimeRule": "08月21日 17:00 - 08月22日 17:00的购买申请，08月25日确认份额",
            "cancelRule": "08月21日 17:00 - 08月22日 17:00的购买申请，08月22日17:00前可撤单",
            "redeemFeeRule": "不收费",
            "redeemWayRule": "",
            "payTimeRule": "银行工作日17：00前赎回，T+1个工作日到账；17：00-24：00赎回，T+2工作日到账（T指当前工作日）",
            "redemptionLimit": "",
            "timeLineData": {
                "confirmDayTips": "08月22日17:00前购买，08月25日可查看持仓并开始计算收益",
                "redeemDayTips": "",
                "redeemWayTips": "",
                "barrierFreeTips": "现在买入买入08月25日确认份额随时可赎",
                "lineData": [
                    {
                        "time": "现在买入",
                        "action": "买入",
                        "active": false
                    },
                    {
                        "time": "08.25",
                        "action": "确认份额",
                        "active": false
                    },
                    {
                        "time": "",
                        "action": "随时可赎",
                        "active": false
                    }
                ],
                "payTimeRuleTips": ""
            },
            "quaInv": "N",
            "pfsVst": "N",
            "navTyp": "A",
            "sesTag": "N",
            "prfCtb": "",
            "buySum": "2,295",
            "jbpTag": "N",
            "jjbTag": "",
            "jjbTs1": "",
            "jjbTs2": "",
            "jjbTs3": "",
            "jjbTs4": "",
            "rcmBrf": "",
            "prfExs": "N",
            "rseFrm": "0",
            "taxPns": "A",
            "clmCtl": "N",
            "basCt2": "Y",
            "basPrf": "中国人民银行公布的7天通知存款利率",
            "basDsc": "本理财产品募集资金投资于法律法规允许投资的金融工具，包括现金、期限在1年以内（含1年）的银行存款、债券回购、中央银行票据、同业存单，剩余期限在397天以内（含397天）的债券、在银行间市场和证券交易所市场发行的资产支持证券，以及银保监会、中国人民银行认可的其他具有良好流动性的金融产品和金融工具。本理财产品业绩比较基准由管理人根据本理财产品的投资范围及比例、投资策略，并综合考虑市场环境等因素设定。",
            "floMan": "",
            "cshPrf": "买入下个工作日享收益",
            "salTag": "A",
            "evlLvl": "1",
            "mgsStd": "",
            "mgsMod": "",
            "mgsRat": "",
            "kzdTag": "",
            "idxDsc": "",
            "cndTid": "",
            "astExt": "N",
            "astShw": "N",
            "astTop": "N",
            "ccyNbr": "元",
            "prf1Mn": "-100.00",
            "prf3Mn": "-100.00",
            "prf6Mn": "-100.00",
            "prf1Ye": "-100.00",
            "prfEst": "1.35",
            "yld1Mn": "-100.00",
            "yld3Mn": "-100.00",
            "yld6Mn": "-100.00",
            "yld1Ye": "-100.00",
            "yldEst": "-100.00",
            "btnTag": "Y",
            "btnTxt": "购买",
            "runDat": "2025-03-20",
            "runDat1": "2025年03月20日",
            "pdbRun": "N",
            "hisSwh": "N",
            "avgRat": "-100",
            "posPby": "-100",
            "hodMon": "0",
            "perBnk": "业绩比较基准：中国人民银行公布的7天通知存款利率。业绩比较基准不是预期收益率，不代表产品的未来表现和实际收益，不构成对产品收益的承诺。本理财产品募集资金投资于法律法规允许投资的金融工具，包括现金、期限在1年以内（含1年）的银行存款、债券回购、中央银行票据、同业存单，剩余期限在397天以内（含397天）的债券、在银行间市场和证券交易所市场发行的资产支持证券，以及银保监会、中国人民银行认可的其他具有良好流动性的金融产品和金融工具。本理财产品业绩比较基准由管理人根据本理财产品的投资范围及比例、投资策略，并综合考虑市场环境等因素设定。",
            "perBnkOpen": "业绩比较基准：中国人民银行公布的7天通知存款利率。业绩比较基准不是预期收益率，不代表产品的未来表现和实际收益，不构成对产品收益的承诺。<br>本理财产品募集资金投资于法律法规允许投资的金融工具，包括现金、期限在1年以内（含1年）的银行存款、债券回购、中央银行票据、同业存单，剩余期限在397天以内（含397天）的债券、在银行间市场和证券交易所市场发行的资产支持证券，以及银保监会、中国人民银行认可的其他具有良好流动性的金融产品和金融工具。本理财产品业绩比较基准由管理人根据本理财产品的投资范围及比例、投资策略，并综合考虑市场环境等因素设定。",
            "prfRsk": "Y",
            "prfDat": "2025年08月21日",
            "clmDes": "",
            "extTag": "",
            "prgSwh": "Y",
            "cfmSwh": "Y",
            "canSwh": "Y",
            "blkFlg": "N",
            "shtQlt": "N",
            "eltShw": "N"
        }
    }
}
```
另外一个是"https://mobile.cmbchina.com/ientrustfinance/product-statistics/get-history-profit?saaCode=D07&ripInn=PA030144&size=30"， 这是获历史收益的接口。
示例如下：
```json
{
    "sysCode": 200,
    "sysMsg": "Success",
    "bizResult": {
        "code": 200,
        "data": [
            {
                "date": "2025-08-21",
                "tenThousandProfit": "0.4749",
                "sevenDaysAnnualProfit": "1.80"
            },
            {
                "date": "2025-08-20",
                "tenThousandProfit": "0.4805",
                "sevenDaysAnnualProfit": "1.81"
            },
            {
                "date": "2025-08-19",
                "tenThousandProfit": "0.4790",
                "sevenDaysAnnualProfit": "1.82"
            },
            {
                "date": "2025-08-18",
                "tenThousandProfit": "0.4826",
                "sevenDaysAnnualProfit": "1.85"
            },
            {
                "date": "2025-08-17",
                "tenThousandProfit": "0.5018",
                "sevenDaysAnnualProfit": "1.87"
            },
            {
                "date": "2025-08-16",
                "tenThousandProfit": "0.5018",
                "sevenDaysAnnualProfit": "1.88"
            },
            {
                "date": "2025-08-15",
                "tenThousandProfit": "0.5018",
                "sevenDaysAnnualProfit": "1.90"
            },
            {
                "date": "2025-08-14",
                "tenThousandProfit": "0.4988",
                "sevenDaysAnnualProfit": "1.91"
            },
            {
                "date": "2025-08-13",
                "tenThousandProfit": "0.5004",
                "sevenDaysAnnualProfit": "1.92"
            },
            {
                "date": "2025-08-12",
                "tenThousandProfit": "0.5251",
                "sevenDaysAnnualProfit": "1.93"
            },
            {
                "date": "2025-08-11",
                "tenThousandProfit": "0.5248",
                "sevenDaysAnnualProfit": "1.93"
            },
            {
                "date": "2025-08-10",
                "tenThousandProfit": "0.5253",
                "sevenDaysAnnualProfit": "1.93"
            },
            {
                "date": "2025-08-09",
                "tenThousandProfit": "0.5253",
                "sevenDaysAnnualProfit": "1.93"
            },
            {
                "date": "2025-08-08",
                "tenThousandProfit": "0.5254",
                "sevenDaysAnnualProfit": "1.92"
            },
            {
                "date": "2025-08-07",
                "tenThousandProfit": "0.5199",
                "sevenDaysAnnualProfit": "1.93"
            },
            {
                "date": "2025-08-06",
                "tenThousandProfit": "0.5276",
                "sevenDaysAnnualProfit": "1.93"
            },
            {
                "date": "2025-08-05",
                "tenThousandProfit": "0.5259",
                "sevenDaysAnnualProfit": "1.93"
            },
            {
                "date": "2025-08-04",
                "tenThousandProfit": "0.5188",
                "sevenDaysAnnualProfit": "1.93"
            },
            {
                "date": "2025-08-03",
                "tenThousandProfit": "0.5192",
                "sevenDaysAnnualProfit": "1.93"
            },
            {
                "date": "2025-08-02",
                "tenThousandProfit": "0.5192",
                "sevenDaysAnnualProfit": "1.97"
            },
            {
                "date": "2025-08-01",
                "tenThousandProfit": "0.5276",
                "sevenDaysAnnualProfit": "2.00"
            },
            {
                "date": "2025-07-31",
                "tenThousandProfit": "0.5366",
                "sevenDaysAnnualProfit": "2.04"
            },
            {
                "date": "2025-07-30",
                "tenThousandProfit": "0.5231",
                "sevenDaysAnnualProfit": "2.07"
            },
            {
                "date": "2025-07-29",
                "tenThousandProfit": "0.5241",
                "sevenDaysAnnualProfit": "2.12"
            },
            {
                "date": "2025-07-28",
                "tenThousandProfit": "0.5231",
                "sevenDaysAnnualProfit": "2.14"
            },
            {
                "date": "2025-07-27",
                "tenThousandProfit": "0.5862",
                "sevenDaysAnnualProfit": "2.17"
            },
            {
                "date": "2025-07-26",
                "tenThousandProfit": "0.5862",
                "sevenDaysAnnualProfit": "2.16"
            },
            {
                "date": "2025-07-25",
                "tenThousandProfit": "0.5863",
                "sevenDaysAnnualProfit": "2.15"
            },
            {
                "date": "2025-07-24",
                "tenThousandProfit": "0.6048",
                "sevenDaysAnnualProfit": "2.16"
            },
            {
                "date": "2025-07-23",
                "tenThousandProfit": "0.6174",
                "sevenDaysAnnualProfit": "2.15"
            }
        ]
    }
}
```

4. 数据处理逻辑
   - 每个理财产品保存信息尽量详细，写入数据库
   - 每个理财产品保存历史收益，写入数据库
   



# 说明

1. 目前只需要支持一个银行的理财产品，后续可以扩展
2. 过程中数据尽量保存，可以用文件
3. 支持定时逻辑