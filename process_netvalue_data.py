#!/usr/bin/env python3
"""
处理净值历史数据并写入数据库
"""

import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from src.nm_crawl.database.connection import init_database, close_database
from src.nm_crawl.services.database_service import CmbDatabaseService


async def process_netvalue_file(file_path: str) -> int:
    """
    处理单个净值历史数据文件
    
    Args:
        file_path: JSON文件路径
        
    Returns:
        int: 保存的记录数量
    """
    logger.info(f"Processing file: {file_path}")
    
    if not Path(file_path).exists():
        logger.error(f"File not found: {file_path}")
        return 0
    
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 从文件名中提取产品代码
        filename = Path(file_path).name
        # 文件名格式: history_profit_get-history-net-value_133030A_20250825_165906_764430.json
        parts = filename.split('_')
        rip_cod = None
        
        for i, part in enumerate(parts):
            if part == "get-history-net-value" and i + 1 < len(parts):
                rip_cod = parts[i + 1]
                break
        
        if not rip_cod:
            logger.error(f"Could not extract ripCod from filename: {filename}")
            return 0
        
        logger.info(f"Extracted product code: {rip_cod}")
        
        # 获取净值数据
        net_value_data = data.get('get-history-net-value', [])
        
        if not net_value_data:
            logger.warning(f"No net value data found in {file_path}")
            return 0
        
        logger.info(f"Found {len(net_value_data)} net value records")
        
        # 显示前几条数据
        logger.info("Sample data:")
        for i, record in enumerate(net_value_data[:3]):
            logger.info(f"  Record {i+1}: {record}")
        
        # 初始化数据库服务
        db_service = CmbDatabaseService()
        
        # 保存净值数据
        crawl_time = datetime.now()
        saved_count = await db_service.save_net_value_history(
            rip_cod, 
            net_value_data, 
            crawl_time
        )
        
        logger.info(f"Successfully saved {saved_count} net value records for product {rip_cod}")
        return saved_count
        
    except Exception as e:
        logger.error(f"Failed to process file {file_path}: {e}")
        import traceback
        traceback.print_exc()
        return 0


async def verify_data_in_database(rip_cod: str):
    """
    验证数据是否正确写入数据库
    
    Args:
        rip_cod: 产品代码
    """
    logger.info(f"Verifying data for product: {rip_cod}")
    
    try:
        from src.nm_crawl.models.financial_models import HistoryNetValue
        from src.nm_crawl.database.connection import get_db_session
        from sqlalchemy import select
        
        async for session in get_db_session():
            # 查询该产品的净值数据
            stmt = select(HistoryNetValue).where(
                HistoryNetValue.rip_cod == rip_cod
            ).order_by(HistoryNetValue.net_value_date.desc()).limit(5)
            
            result = await session.execute(stmt)
            records = result.scalars().all()
            
            logger.info(f"Found {len(records)} net value records in database")
            
            for i, record in enumerate(records):
                logger.info(f"Record {i+1}:")
                logger.info(f"  Date: {record.net_value_date}")
                logger.info(f"  Unit Net Value: {record.unit_net_value}")
                logger.info(f"  Total Net Value: {record.total_net_value}")
                logger.info(f"  Net Value Change: {record.net_value_change}")
                logger.info(f"  Show Provision: {record.show_provision}")
                
    except Exception as e:
        logger.error(f"Failed to verify data: {e}")
        import traceback
        traceback.print_exc()


async def get_database_statistics():
    """获取数据库统计信息"""
    try:
        db_service = CmbDatabaseService()
        
        product_count = await db_service.get_product_count()
        history_count = await db_service.get_history_count()
        net_value_count = await db_service.get_net_value_count()
        
        logger.info("Database Statistics:")
        logger.info(f"  Products: {product_count}")
        logger.info(f"  History Records: {history_count}")
        logger.info(f"  Net Value Records: {net_value_count}")
        
    except Exception as e:
        logger.error(f"Failed to get database statistics: {e}")


async def main():
    """主函数"""
    logger.info("🏦 Processing Net Value History Data")
    logger.info("=" * 60)
    
    # 指定要处理的文件
    file_path = "data/history/history_profit_get-history-net-value_133030A_20250825_165906_764430.json"
    
    try:
        # 初始化数据库
        logger.info("Initializing database...")
        await init_database()
        logger.info("✅ Database initialized successfully")
        
        # 处理文件
        saved_count = await process_netvalue_file(file_path)
        
        if saved_count > 0:
            # 验证数据
            await verify_data_in_database("133030A")
            
            # 显示统计信息
            await get_database_statistics()
            
            logger.info(f"✅ Successfully processed {saved_count} records!")
        else:
            logger.warning("❌ No records were saved")
        
        # 关闭数据库连接
        await close_database()
        logger.info("✅ Database connection closed")
        
        print("\n💡 提示:")
        print("- 数据库文件位置: data/financial_products.db")
        print("- 新表名: history_netvalue")
        print("- 可以使用SQLite工具查询: SELECT * FROM history_netvalue LIMIT 10;")
        
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 设置日志
    logger.remove()
    logger.add(
        sys.stdout, 
        level="INFO", 
        format="{time:HH:mm:ss} | {level} | {message}",
        colorize=True
    )
    
    # 运行处理
    asyncio.run(main())
