"""
显示数据库表结构和字段描述
由于SQLite不完全支持字段注释显示，这个工具从模型定义中提取描述信息
"""

import sys
from pathlib import Path
import asyncio
from typing import Dict, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.models.financial_models import (
    ProductList, ProductDetail, HistoryProfits, HistoryNetValue, HistoryPerformance
)
from src.nm_crawl.database.connection import init_database
from sqlalchemy import text


def extract_model_info(model_class) -> Dict[str, Tuple[str, str, str]]:
    """
    从SQLAlchemy模型中提取字段信息
    
    Returns:
        Dict[field_name, (field_type, nullable, comment)]
    """
    fields = {}
    
    for column_name, column in model_class.__table__.columns.items():
        field_type = str(column.type)
        nullable = "NULL" if column.nullable else "NOT NULL"
        comment = getattr(column, 'comment', '') or ''
        
        fields[column_name] = (field_type, nullable, comment)
    
    return fields


def print_table_schema(table_name: str, model_class, fields: Dict[str, Tuple[str, str, str]]):
    """打印表结构"""
    
    print(f"\n{'='*80}")
    print(f"表名: {table_name}")
    print(f"模型类: {model_class.__name__}")
    print(f"表注释: {model_class.__doc__ or '无'}")
    print(f"{'='*80}")
    
    # 表头
    print(f"{'字段名':<20} {'类型':<15} {'约束':<10} {'描述'}")
    print(f"{'-'*20} {'-'*15} {'-'*10} {'-'*30}")
    
    # 字段信息
    for field_name, (field_type, nullable, comment) in fields.items():
        print(f"{field_name:<20} {field_type:<15} {nullable:<10} {comment}")
    
    # 索引信息
    if hasattr(model_class, '__table_args__') and model_class.__table_args__:
        print(f"\n索引和约束:")
        for constraint in model_class.__table_args__:
            if hasattr(constraint, 'name'):
                constraint_type = type(constraint).__name__
                if hasattr(constraint, 'columns'):
                    columns = [col.name for col in constraint.columns]
                    print(f"  - {constraint_type}: {constraint.name} ({', '.join(columns)})")
                else:
                    print(f"  - {constraint_type}: {constraint.name}")


async def show_database_schema():
    """显示数据库架构"""
    
    print("理财产品数据库表结构")
    print("="*80)
    
    # 表定义
    tables = [
        ("product_list", ProductList),
        ("product_detail", ProductDetail), 
        ("history_profits", HistoryProfits),
        ("history_netvalue", HistoryNetValue),
        ("history_performance", HistoryPerformance),  # 新增的表
    ]
    
    # 显示每个表的结构
    for table_name, model_class in tables:
        fields = extract_model_info(model_class)
        print_table_schema(table_name, model_class, fields)
    
    # 显示表关系
    print(f"\n{'='*80}")
    print("表关系说明:")
    print("="*80)
    print("1. product_list: 产品列表主表")
    print("   - 存储产品基本信息和列表数据")
    print("   - 主键: product_id (格式: cmb_产品代码)")
    
    print("\n2. product_detail: 产品详情表")
    print("   - 存储产品详细信息")
    print("   - 关联: product_id -> product_list.product_id")
    
    print("\n3. history_profits: 历史收益表")
    print("   - 存储万份收益和七日年化收益数据")
    print("   - 关联: product_id -> product_list.product_id")
    print("   - 唯一约束: (product_id, profit_date)")
    
    print("\n4. history_netvalue: 历史净值表")
    print("   - 存储单位净值、累计净值等数据")
    print("   - 关联: product_id -> product_list.product_id")
    print("   - 唯一约束: (product_id, net_value_date)")
    
    print("\n5. history_performance: 历史业绩表 [新增]")
    print("   - 存储不同时间区间的业绩数据")
    print("   - 关联: product_id -> product_list.product_id")
    print("   - 唯一约束: (product_id, prf_typ, time_interval)")
    print("   - 业绩类型: A=近1月, B=近3月, J=今年以来, F=成立以来")


async def show_actual_database_info():
    """显示实际数据库中的表信息"""
    
    print(f"\n{'='*80}")
    print("实际数据库表信息:")
    print("="*80)
    
    try:
        # 初始化数据库
        manager = await init_database()
        
        # 查询所有表
        async with manager.engine.begin() as conn:
            result = await conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
            tables = result.fetchall()
            
            print(f"数据库中的表: {[table[0] for table in tables]}")
            
            # 显示每个表的记录数
            for table in tables:
                table_name = table[0]
                if not table_name.startswith('sqlite_'):
                    try:
                        count_result = await conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                        count = count_result.fetchone()[0]
                        print(f"  - {table_name}: {count} 条记录")
                    except Exception as e:
                        print(f"  - {table_name}: 查询失败 ({e})")
        
        # 显示 history_performance 表的示例数据
        print(f"\n{'='*80}")
        print("history_performance 表示例数据:")
        print("="*80)
        
        async with manager.engine.begin() as conn:
            result = await conn.execute(text("""
                SELECT product_id, prf_typ, time_interval, net_value_change, yea_yld, created_at
                FROM history_performance 
                ORDER BY created_at DESC 
                LIMIT 5
            """))
            records = result.fetchall()
            
            if records:
                print(f"{'产品ID':<15} {'类型':<6} {'时间区间':<12} {'净值变化':<10} {'年化收益':<10} {'创建时间'}")
                print(f"{'-'*15} {'-'*6} {'-'*12} {'-'*10} {'-'*10} {'-'*19}")
                
                for record in records:
                    product_id = record[0][:12] + "..." if len(record[0]) > 15 else record[0]
                    print(f"{product_id:<15} {record[1]:<6} {record[2]:<12} {record[3]:<10} {record[4]:<10} {str(record[5])[:19]}")
            else:
                print("暂无数据")
                
    except Exception as e:
        print(f"查询数据库信息失败: {e}")


async def main():
    """主函数"""
    
    # 显示模型定义的表结构
    await show_database_schema()
    
    # 显示实际数据库信息
    await show_actual_database_info()
    
    print(f"\n{'='*80}")
    print("说明:")
    print("- SQLite 数据库不完全支持字段注释的显示")
    print("- 上述字段描述来自 SQLAlchemy 模型定义")
    print("- 实际数据库中的字段类型可能略有不同")
    print("- 新增的 history_performance 表已成功创建并可正常使用")
    print("="*80)


if __name__ == "__main__":
    asyncio.run(main())
