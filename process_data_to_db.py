#!/usr/bin/env python3
"""
将爬取的JSON数据处理并写入数据库
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.services.data_processor import DataProcessor
from src.nm_crawl.utils.logger import setup_logging
from loguru import logger


async def main():
    """主函数"""
    # 设置日志
    setup_logging(level="INFO", enable_file_logging=True)
    
    logger.info("开始处理数据到数据库")
    
    try:
        # 创建数据处理器
        processor = DataProcessor("data")
        
        # 获取处理前的统计信息
        logger.info("获取处理前的数据库统计...")
        before_stats = await processor.get_database_stats()
        logger.info(f"处理前统计: {before_stats}")
        
        # 处理所有数据
        logger.info("开始处理数据文件...")
        results = await processor.process_all_data()
        
        # 获取处理后的统计信息
        logger.info("获取处理后的数据库统计...")
        after_stats = await processor.get_database_stats()
        logger.info(f"处理后统计: {after_stats}")
        
        # 显示结果
        print("\n" + "="*60)
        print("📊 数据处理结果")
        print("="*60)
        print(f"✅ 产品列表: 处理了 {results['products']} 个产品")
        print(f"✅ 产品详情: 处理了 {results['details']} 个详情")
        print(f"✅ 历史收益: 处理了 {results['history']} 条历史记录")
        
        print("\n📈 数据库统计")
        print("-"*30)
        print(f"📦 总产品数: {before_stats['products']} → {after_stats['products']}")
        print(f"📊 总历史记录: {before_stats['history_records']} → {after_stats['history_records']}")
        
        if results['products'] > 0 or results['details'] > 0 or results['history'] > 0:
            print("\n🎉 数据处理成功完成！")
            print("\n💡 现在您可以:")
            print("1. 查看数据库文件: data/financial_products.db")
            print("2. 使用SQL工具查询数据")
            print("3. 运行其他分析脚本")
        else:
            print("\n⚠️  没有处理任何数据")
            print("请检查data目录下是否有数据文件")
        
    except Exception as e:
        logger.error(f"数据处理失败: {e}")
        import traceback
        traceback.print_exc()
        print("\n❌ 数据处理失败")
        print("请检查日志文件获取详细错误信息")


if __name__ == "__main__":
    asyncio.run(main())
