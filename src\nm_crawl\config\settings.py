"""
Configuration management for the crawler
"""

import os
import json
from typing import Dict, Any, Optional
from pathlib import Path
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv


class DatabaseConfig(BaseModel):
    """数据库配置"""
    url: str = Field(default="sqlite+aiosqlite:///data/financial_products.db", description="数据库连接URL")
    echo: bool = Field(default=False, description="是否输出SQL语句")
    pool_size: int = Field(default=5, description="连接池大小")
    max_overflow: int = Field(default=10, description="最大溢出连接数")


class CrawlerConfig(BaseModel):
    """爬虫配置"""
    list_wait_time: int = Field(default=20, description="列表页等待时间（秒）")
    detail_wait_time: int = Field(default=15, description="详情页等待时间（秒）")
    detail_delay: int = Field(default=3, description="详情页请求间延迟（秒）")
    max_scroll_attempts: int = Field(default=5, description="最大滚动尝试次数")
    max_detail_products: int = Field(default=50, description="最大详情爬取产品数")
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: int = Field(default=300, description="重试延迟（秒）")
    max_concurrent_tasks: int = Field(default=2, description="最大并发任务数")
    headless: bool = Field(default=True, description="是否使用无头浏览器")
    
    @validator('list_wait_time', 'detail_wait_time')
    def validate_wait_time(cls, v):
        if v < 5:
            raise ValueError('Wait time must be at least 5 seconds')
        return v
    
    @validator('max_detail_products')
    def validate_max_products(cls, v):
        if v < 1:
            raise ValueError('Max detail products must be at least 1')
        return v


class SchedulerConfig(BaseModel):
    """调度器配置"""
    enable_scheduler: bool = Field(default=False, description="是否启用调度器")
    full_crawl_interval: str = Field(default="daily", description="完整爬取间隔")
    list_crawl_interval: str = Field(default="4h", description="列表爬取间隔")
    full_crawl_time: str = Field(default="09:00", description="完整爬取时间")
    auto_start: bool = Field(default=False, description="是否自动启动调度器")


class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = Field(default="INFO", description="日志级别")
    log_directory: str = Field(default="logs", description="日志目录")
    enable_file_logging: bool = Field(default=True, description="是否启用文件日志")
    max_log_size: str = Field(default="10 MB", description="最大日志文件大小")
    log_retention: str = Field(default="30 days", description="日志保留时间")
    
    @validator('level')
    def validate_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of {valid_levels}')
        return v.upper()


class StorageConfig(BaseModel):
    """存储配置"""
    data_directory: str = Field(default="data", description="数据存储目录")
    backup_enabled: bool = Field(default=True, description="是否启用数据备份")
    backup_retention_days: int = Field(default=30, description="备份保留天数")
    compress_backups: bool = Field(default=True, description="是否压缩备份")


class AppConfig(BaseModel):
    """应用配置"""
    app_name: str = Field(default="nm_crawl", description="应用名称")
    version: str = Field(default="0.1.0", description="应用版本")
    debug: bool = Field(default=False, description="是否启用调试模式")
    cdp_url: str = Field(default="", description="cdp_url")
    
    # 子配置
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    crawler: CrawlerConfig = Field(default_factory=CrawlerConfig)
    scheduler: SchedulerConfig = Field(default_factory=SchedulerConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    storage: StorageConfig = Field(default_factory=StorageConfig)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json", env_file: str = ".env"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
            env_file: 环境变量文件路径
        """
        self.config_file = config_file
        self.env_file = env_file
        self.config: Optional[AppConfig] = None
        
        # 加载环境变量
        if os.path.exists(env_file):
            load_dotenv(env_file)
    
    def load_config(self) -> AppConfig:
        """
        加载配置
        
        Returns:
            AppConfig: 应用配置
        """
        config_data = {}
        
        # 从文件加载配置
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
            except Exception as e:
                print(f"Warning: Failed to load config file {self.config_file}: {e}")
        
        # 从环境变量覆盖配置
        env_overrides = self._get_env_overrides()
        config_data = self._merge_config(config_data, env_overrides)
        
        # 创建配置对象
        self.config = AppConfig(**config_data)
        return self.config
    
    def save_config(self, config: AppConfig = None) -> str:
        """
        保存配置到文件
        
        Args:
            config: 要保存的配置，默认使用当前配置
            
        Returns:
            str: 配置文件路径
        """
        if config is None:
            config = self.config
        
        if config is None:
            raise ValueError("No config to save")
        
        # 确保配置目录存在
        config_dir = os.path.dirname(self.config_file)
        if config_dir:
            os.makedirs(config_dir, exist_ok=True)
        
        # 保存配置
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config.dict(), f, ensure_ascii=False, indent=2)
        
        return self.config_file
    
    def _get_env_overrides(self) -> Dict[str, Any]:
        """
        从环境变量获取配置覆盖
        
        Returns:
            Dict[str, Any]: 环境变量配置
        """
        overrides = {}
        
        # 数据库配置
        if os.getenv('DATABASE_URL'):
            overrides.setdefault('database', {})['url'] = os.getenv('DATABASE_URL')
        
        if os.getenv('DATABASE_ECHO'):
            overrides.setdefault('database', {})['echo'] = os.getenv('DATABASE_ECHO').lower() == 'true'
        
        # 爬虫配置
        if os.getenv('CRAWLER_HEADLESS'):
            overrides.setdefault('crawler', {})['headless'] = os.getenv('CRAWLER_HEADLESS').lower() == 'true'
        
        if os.getenv('CRAWLER_MAX_PRODUCTS'):
            overrides.setdefault('crawler', {})['max_detail_products'] = int(os.getenv('CRAWLER_MAX_PRODUCTS'))
        
        # 调度器配置
        if os.getenv('SCHEDULER_ENABLED'):
            overrides.setdefault('scheduler', {})['enable_scheduler'] = os.getenv('SCHEDULER_ENABLED').lower() == 'true'
        
        if os.getenv('SCHEDULER_AUTO_START'):
            overrides.setdefault('scheduler', {})['auto_start'] = os.getenv('SCHEDULER_AUTO_START').lower() == 'true'
        
        # 日志配置
        if os.getenv('LOG_LEVEL'):
            overrides.setdefault('logging', {})['level'] = os.getenv('LOG_LEVEL')
        
        if os.getenv('LOG_DIRECTORY'):
            overrides.setdefault('logging', {})['log_directory'] = os.getenv('LOG_DIRECTORY')
        
        # 存储配置
        if os.getenv('DATA_DIRECTORY'):
            overrides.setdefault('storage', {})['data_directory'] = os.getenv('DATA_DIRECTORY')
        
        # 应用配置
        if os.getenv('DEBUG'):
            overrides['debug'] = os.getenv('DEBUG').lower() == 'true'
        
        return overrides
    
    def _merge_config(self, base: Dict[str, Any], overrides: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并配置字典
        
        Args:
            base: 基础配置
            overrides: 覆盖配置
            
        Returns:
            Dict[str, Any]: 合并后的配置
        """
        result = base.copy()
        
        for key, value in overrides.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def get_config(self) -> AppConfig:
        """
        获取当前配置
        
        Returns:
            AppConfig: 当前配置
        """
        if self.config is None:
            self.config = self.load_config()
        return self.config
    
    def create_default_config(self) -> str:
        """
        创建默认配置文件
        
        Returns:
            str: 配置文件路径
        """
        default_config = AppConfig()
        return self.save_config(default_config)
    
    def create_example_env(self, env_file: str = ".env.example") -> str:
        """
        创建示例环境变量文件
        
        Args:
            env_file: 环境变量文件路径
            
        Returns:
            str: 环境变量文件路径
        """
        example_content = """# Database Configuration
DATABASE_URL=sqlite+aiosqlite:///data/financial_products.db
DATABASE_ECHO=false

# Crawler Configuration
CRAWLER_HEADLESS=true
CRAWLER_MAX_PRODUCTS=50

# Scheduler Configuration
SCHEDULER_ENABLED=false
SCHEDULER_AUTO_START=false

# Logging Configuration
LOG_LEVEL=INFO
LOG_DIRECTORY=logs

# Storage Configuration
DATA_DIRECTORY=data

# Application Configuration
DEBUG=false
"""
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(example_content)
        
        return env_file


# 全局配置管理器实例
config_manager = ConfigManager()


def get_config() -> AppConfig:
    """
    获取全局配置
    
    Returns:
        AppConfig: 应用配置
    """
    return config_manager.get_config()


def load_config(config_file: str = "config.json") -> AppConfig:
    """
    加载配置文件
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        AppConfig: 应用配置
    """
    global config_manager
    config_manager = ConfigManager(config_file)
    return config_manager.load_config()


def save_config(config: AppConfig, config_file: str = "config.json") -> str:
    """
    保存配置到文件
    
    Args:
        config: 应用配置
        config_file: 配置文件路径
        
    Returns:
        str: 配置文件路径
    """
    manager = ConfigManager(config_file)
    return manager.save_config(config)
