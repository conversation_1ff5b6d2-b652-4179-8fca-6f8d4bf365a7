# 表重命名总结

## 🎯 重命名目标

根据您的要求：
1. **删除** `financial_products` 和旧版 `history_profits` 表
2. **重命名** `product_history` → `history_profits`

## 📊 当前数据库状态

### 实际存在的表
```
📋 数据库中的表: 
- crawl_logs (系统日志表)
- financial_products (❌ 需要删除的旧版表)
- history_profits (❌ 需要删除的旧版表，字段名是 date)
- product_details (✅ 新版表)
- product_history (🔄 需要重命名为 history_profits)
- product_list (✅ 新版表)
```

### 表结构对比

#### 旧版 `history_profits` (需要删除)
```sql
- id: INTEGER
- product_id: INTEGER  -- 外键关联
- rip_cod: VARCHAR(50)
- saa_cod: VARCHAR(50)
- date: VARCHAR(20)    -- ❌ 字段名是 date
- ten_thousand_profit: VARCHAR(20)
- seven_days_annual_profit: VARCHAR(20)
- created_at: DATETIME
```

#### 新版 `product_history` (需要重命名)
```sql
- id: INTEGER
- product_id: VARCHAR(100)  -- 字符串ID (cmb_+ripCod)
- rip_cod: VARCHAR(50)
- profit_date: VARCHAR(20)  -- ✅ 字段名是 profit_date
- ten_thousand_profit: VARCHAR(20)
- seven_days_annual_profit: VARCHAR(20)
- created_at: DATETIME
- crawl_time: DATETIME
```

## 🐛 当前问题

1. **字段名冲突**: 代码使用 `profit_date`，但旧版表使用 `date`
2. **表共存**: 新旧两套表同时存在，造成混淆
3. **模型残留**: 某处仍在创建旧版表结构

## 🔧 已完成的修改

### ✅ 模型层修改
- 删除了 `src/nm_crawl/models/financial_product.py`
- 重命名了 `ProductHistory` → `HistoryProfits`
- 更新了表名 `product_history` → `history_profits`

### ✅ 服务层修改
- 更新了 `src/nm_crawl/services/database_service.py` 中的导入
- 更新了所有对 `ProductHistory` 的引用

### ✅ 查询层修改
- 更新了 `query_database.py` 中的导入和引用

### ✅ 数据库连接修改
- 移除了对旧版模型的导入

## ❌ 仍存在的问题

### 问题根源
旧版表仍然被创建，可能的原因：
1. **缓存的元数据**: SQLAlchemy 可能缓存了旧版表结构
2. **其他模块导入**: 某个模块仍在导入旧版模型
3. **数据库文件残留**: 旧版表结构仍在数据库文件中

### 错误信息
```
(sqlite3.OperationalError) table history_profits has no column named profit_date
```

## 🚀 解决方案

### 方案1: 完全重建数据库
1. 删除数据库文件
2. 确保没有旧版模型被导入
3. 重新创建数据库

### 方案2: 手动删除旧版表
1. 连接数据库
2. 删除 `financial_products` 和旧版 `history_profits` 表
3. 重新运行数据处理

### 方案3: 修复字段名映射
1. 临时修改代码使用 `date` 字段名
2. 处理完数据后再重命名

## 💡 建议的执行步骤

1. **清理残留引用**
   ```bash
   # 搜索所有可能的旧版模型引用
   grep -r "financial_product" src/
   grep -r "FinancialProduct" src/
   grep -r "HistoryProfit" src/
   ```

2. **强制重建数据库**
   ```bash
   rm data/financial_products.db
   python process_data_to_db.py
   ```

3. **验证表结构**
   ```bash
   python test_table_structure.py
   ```

## 🎯 期望的最终状态

```
📋 最终数据库表:
- product_list (产品列表)
- product_details (产品详情)  
- history_profits (历史收益，来自原 product_history)
- crawl_logs (系统日志)
```

**🔄 当前状态**: 部分完成，需要解决旧版表残留问题
**🎯 目标状态**: `product_history` → `history_profits` 重命名完成
