# 字段管理指南

## 📋 概述

为了保持数据库表结构的灵活性，我们采用了**注释保留**的方式来管理产品详情表的字段。这样既能保持当前的精简高效，又能在需要时快速启用额外字段。

## 🎯 当前启用的字段

### `product_details` 表当前启用的字段：

```python
# 基础信息
product_id = Column(String(100), primary_key=True, comment="产品ID (cmb_+ripCod)")
rip_cod = Column(String(50), nullable=False, index=True, comment="产品代码")
saa_cod = Column(String(50), comment="销售代码")
rip_snm = Column(String(200), nullable=False, comment="产品名称")

# 发行机构信息
crp_nam = Column(String(200), comment="发行机构名称")
crp_cod = Column(String(50), comment="发行机构代码")

# 收益率信息
rate_text = Column(String(100), comment="收益率显示文本")
rate_name = Column(String(50), comment="收益率名称")

# 产品基本信息
ter_day = Column(String(200), comment="投资期限描述")
risk_lvl = Column(String(50), comment="风险等级")
sbs_uqt = Column(String(100), comment="起购金额")
invest_typ = Column(String(50), comment="投资类型")

# 交易规则（核心）
buy_time_rule = Column(Text, comment="销售时间规则")
pay_time_rule = Column(Text, comment="到账时间规则")

# 其他重要信息
buy_sum = Column(String(50), comment="购买人数")
csh_prf = Column(String(200), comment="现金收益描述")

# 产品特色
features = Column(JSON, comment="产品特色列表")

# 系统字段
raw_data = Column(JSON, comment="完整原始数据")
crawl_time = Column(DateTime, comment="爬取时间")
created_at = Column(DateTime, default=func.now(), comment="创建时间")
updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
```

## 💤 注释保留的字段

### 基础信息扩展
```python
# rip_nbr = Column(String(50), comment="产品编号")
# rip_inn = Column(String(50), comment="产品内部编号")
```

### 发行机构详细信息
```python
# crp_dec = Column(Text, comment="发行机构描述")
```

### 收益率详细信息
```python
# rate_text_top = Column(String(100), comment="顶部收益率显示文本")
# rat_dsc = Column(String(10), comment="收益率描述代码")
```

### 产品信息扩展
```python
# ter_day_name = Column(String(50), comment="投资期限名称")
# lmt_cyc = Column(String(100), comment="限制周期")
```

### 详细交易规则
```python
# buy_time_name = Column(String(50), comment="销售时间名称")
# buy_fee_rule = Column(String(100), comment="申购费用规则")
# confirm_time_rule = Column(Text, comment="确认时间规则")
# cancel_rule = Column(Text, comment="撤单规则")
# redeem_fee_rule = Column(String(100), comment="赎回费用规则")
# redeem_way_rule = Column(Text, comment="赎回方式规则")
# redemption_rule = Column(Text, comment="赎回规则")
# redemption_limit = Column(Text, comment="赎回限制")
```

### 其他信息
```python
# bas_dsc = Column(Text, comment="基础描述")
# evl_lvl = Column(String(10), comment="评级等级")
# tab_show = Column(String(100), comment="标签显示")
# sell_point = Column(Text, comment="卖点")
```

### 产品状态标志
```python
# qua_inv = Column(String(10), comment="合格投资者标志")
# pfs_vst = Column(String(10), comment="专业投资者标志")
# nav_typ = Column(String(10), comment="净值类型")
# ses_tag = Column(String(10), comment="会话标签")
# prf_ctb = Column(String(10), comment="收益贡献标志")
# jbp_tag = Column(String(10), comment="基本点标签")
# jjb_tag = Column(String(50), comment="基金宝标签")
# prf_exs = Column(String(10), comment="收益存在标志")
# rse_frm = Column(String(10), comment="风险形式")
# tax_pns = Column(String(10), comment="税收惩罚")
# clm_ctl = Column(String(10), comment="索赔控制")
# bas_ct2 = Column(String(10), comment="基础控制2")
# bas_prf = Column(String(100), comment="基础收益")
# flo_man = Column(String(100), comment="浮动管理")
# sal_tag = Column(String(10), comment="销售标签")
# mgs_std = Column(String(100), comment="管理标准")
# mgs_mod = Column(String(100), comment="管理模式")
```

### 复杂数据结构
```python
# timeline_data = Column(JSON, comment="时间线数据")
```

## 🔧 如何启用注释的字段

### 步骤1: 启用模型字段
在 `src/nm_crawl/models/cmb_models.py` 中，将需要的字段取消注释：

```python
# 例如启用产品编号字段
rip_nbr = Column(String(50), comment="产品编号")  # 取消注释
```

### 步骤2: 更新数据库服务
在 `src/nm_crawl/services/database_service.py` 中，将对应的字段处理取消注释：

```python
# 例如启用产品编号字段的数据处理
rip_nbr=detail_data.get('ripNbr'),  # 取消注释
```

### 步骤3: 重新创建数据库表
```bash
# 删除现有数据库文件
rm data/financial_products.db

# 重新处理数据
python process_data_to_db.py
```

## 💡 使用建议

### 常用字段组合

1. **基础扩展包**：
   - `rip_nbr` (产品编号)
   - `crp_dec` (发行机构描述)
   - `bas_dsc` (基础描述)

2. **交易规则扩展包**：
   - `buy_fee_rule` (申购费用规则)
   - `redeem_fee_rule` (赎回费用规则)
   - `confirm_time_rule` (确认时间规则)

3. **状态监控包**：
   - `nav_typ` (净值类型)
   - `prf_ctb` (收益贡献标志)
   - `evl_lvl` (评级等级)

### 启用原则

1. **按需启用**：只启用业务确实需要的字段
2. **批量启用**：相关字段一起启用，保持数据完整性
3. **测试验证**：启用后进行充分测试
4. **文档更新**：及时更新相关文档

## 🚀 优势

1. **灵活性**：可根据业务需求随时调整字段
2. **可维护性**：字段定义保留在代码中，便于理解
3. **向后兼容**：启用字段不影响现有功能
4. **性能优化**：只使用必要字段，保持高性能
5. **快速扩展**：无需重新设计，快速响应需求变化

**💡 这种设计让您的数据库既保持了当前的高效性，又具备了未来扩展的灵活性！**
