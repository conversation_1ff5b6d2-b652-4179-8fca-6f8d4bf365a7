"""
Task scheduler for automated crawling
"""

import asyncio
import schedule
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from loguru import logger
from enum import Enum

from ..crawlers.list_crawler import FinancialProductListCrawler
from ..crawlers.detail_crawler import FinancialProductDetailCrawler
from ..services.data_processor import DataProcessor


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class CrawlTask:
    """爬取任务"""
    
    def __init__(self, task_id: str, task_type: str, config: Dict[str, Any]):
        """
        初始化爬取任务
        
        Args:
            task_id: 任务ID
            task_type: 任务类型 (list, detail, full)
            config: 任务配置
        """
        self.task_id = task_id
        self.task_type = task_type
        self.config = config
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.error_message = None
        self.retry_count = 0
        self.max_retries = config.get("max_retries", 3)
        self.result = None
    
    def start(self):
        """开始任务"""
        self.status = TaskStatus.RUNNING
        self.started_at = datetime.now()
        logger.info(f"Task {self.task_id} started")
    
    def complete(self, result: Any = None):
        """完成任务"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now()
        self.result = result
        logger.info(f"Task {self.task_id} completed")
    
    def fail(self, error_message: str):
        """任务失败"""
        self.status = TaskStatus.FAILED
        self.completed_at = datetime.now()
        self.error_message = error_message
        logger.error(f"Task {self.task_id} failed: {error_message}")
    
    def cancel(self):
        """取消任务"""
        self.status = TaskStatus.CANCELLED
        self.completed_at = datetime.now()
        logger.info(f"Task {self.task_id} cancelled")
    
    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return self.retry_count < self.max_retries and self.status == TaskStatus.FAILED
    
    def retry(self):
        """重试任务"""
        if self.can_retry():
            self.retry_count += 1
            self.status = TaskStatus.PENDING
            self.error_message = None
            logger.info(f"Task {self.task_id} retry {self.retry_count}/{self.max_retries}")
            return True
        return False


class CrawlScheduler:
    """爬取调度器"""
    
    def __init__(self, save_directory: str = "data"):
        """
        初始化调度器
        
        Args:
            save_directory: 数据保存目录
        """
        self.save_directory = save_directory
        self.list_crawler = FinancialProductListCrawler(save_directory=save_directory)
        self.detail_crawler = FinancialProductDetailCrawler(save_directory=save_directory)
        self.data_processor = DataProcessor(save_directory)
        
        self.tasks = {}  # 任务存储
        self.running_tasks = set()  # 正在运行的任务
        self.is_running = False
        self.scheduler_task = None
        
        # 默认配置
        self.default_config = {
            "list_wait_time": 20,
            "detail_wait_time": 15,
            "detail_delay": 3,
            "max_retries": 3,
            "retry_delay": 300,  # 5分钟
            "max_concurrent_tasks": 2
        }
        
        logger.info("CrawlScheduler initialized")
    
    def create_task(self, task_type: str, config: Dict[str, Any] = None) -> str:
        """
        创建爬取任务
        
        Args:
            task_type: 任务类型 (list, detail, full)
            config: 任务配置
            
        Returns:
            str: 任务ID
        """
        task_id = f"{task_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 合并配置
        task_config = {**self.default_config}
        if config:
            task_config.update(config)
        
        task = CrawlTask(task_id, task_type, task_config)
        self.tasks[task_id] = task
        
        logger.info(f"Created task: {task_id} ({task_type})")
        return task_id
    
    async def execute_list_task(self, task: CrawlTask) -> Dict[str, Any]:
        """
        执行列表爬取任务
        
        Args:
            task: 爬取任务
            
        Returns:
            Dict[str, Any]: 任务结果
        """
        logger.info(f"Executing list task: {task.task_id}")
        
        wait_time = task.config.get("list_wait_time", 20)
        max_scroll_attempts = task.config.get("max_scroll_attempts", 5)
        
        products = await self.list_crawler.crawl_product_list(wait_time, max_scroll_attempts)
        
        result = {
            "task_id": task.task_id,
            "task_type": "list",
            "products_count": len(products),
            "products": products,
            "crawl_time": datetime.now().isoformat()
        }
        
        return result
    
    async def execute_detail_task(self, task: CrawlTask) -> Dict[str, Any]:
        """
        执行详情爬取任务
        
        Args:
            task: 爬取任务
            
        Returns:
            Dict[str, Any]: 任务结果
        """
        logger.info(f"Executing detail task: {task.task_id}")
        
        product_codes = task.config.get("product_codes", [])
        if not product_codes:
            raise ValueError("No product codes provided for detail task")
        
        wait_time = task.config.get("detail_wait_time", 15)
        delay = task.config.get("detail_delay", 3)
        
        results = await self.detail_crawler.crawl_multiple_products(
            product_codes, wait_time, delay
        )
        
        return results
    
    async def execute_full_task(self, task: CrawlTask) -> Dict[str, Any]:
        """
        执行完整爬取任务（列表+详情）
        
        Args:
            task: 爬取任务
            
        Returns:
            Dict[str, Any]: 任务结果
        """
        logger.info(f"Executing full task: {task.task_id}")
        
        # 先爬取列表
        list_wait_time = task.config.get("list_wait_time", 20)
        max_scroll_attempts = task.config.get("max_scroll_attempts", 5)
        
        products = await self.list_crawler.crawl_product_list(list_wait_time, max_scroll_attempts)
        
        if not products:
            raise ValueError("No products found in list crawling")
        
        # 获取产品代码
        product_codes = []
        for product in products:
            rip_cod = product.get("ripCod")
            saa_cod = product.get("saaCod")
            if rip_cod and saa_cod:
                product_codes.append((rip_cod, saa_cod))
        
        # 限制详情爬取数量（可配置）
        max_details = task.config.get("max_detail_products", 10)
        if max_details and len(product_codes) > max_details:
            product_codes = product_codes[:max_details]
            logger.info(f"Limited detail crawling to {max_details} products")
        
        # 爬取详情
        detail_wait_time = task.config.get("detail_wait_time", 15)
        detail_delay = task.config.get("detail_delay", 3)
        
        detail_results = await self.detail_crawler.crawl_multiple_products(
            product_codes, detail_wait_time, detail_delay
        )
        
        # 保存数据到数据库
        storage_result = await self.data_processor.process_all_data()
        
        result = {
            "task_id": task.task_id,
            "task_type": "full",
            "products_count": len(products),
            "details_count": len(detail_results.get("details", {})),
            "history_count": len(detail_results.get("history", {})),
            "storage_result": storage_result,
            "crawl_time": datetime.now().isoformat()
        }
        
        return result
    
    async def execute_task(self, task_id: str) -> bool:
        """
        执行任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否执行成功
        """
        if task_id not in self.tasks:
            logger.error(f"Task not found: {task_id}")
            return False
        
        task = self.tasks[task_id]
        
        if task.status != TaskStatus.PENDING:
            logger.warning(f"Task {task_id} is not pending: {task.status}")
            return False
        
        if task_id in self.running_tasks:
            logger.warning(f"Task {task_id} is already running")
            return False
        
        # 检查并发限制
        if len(self.running_tasks) >= self.default_config["max_concurrent_tasks"]:
            logger.info(f"Max concurrent tasks reached, task {task_id} will wait")
            return False
        
        self.running_tasks.add(task_id)
        task.start()
        
        try:
            # 根据任务类型执行
            if task.task_type == "list":
                result = await self.execute_list_task(task)
            elif task.task_type == "detail":
                result = await self.execute_detail_task(task)
            elif task.task_type == "full":
                result = await self.execute_full_task(task)
            else:
                raise ValueError(f"Unknown task type: {task.task_type}")
            
            task.complete(result)
            return True
            
        except Exception as e:
            error_msg = str(e)
            task.fail(error_msg)
            
            # 检查是否可以重试
            if task.can_retry():
                logger.info(f"Task {task_id} will be retried")
                # 延迟重试
                retry_delay = task.config.get("retry_delay", 300)
                asyncio.create_task(self._schedule_retry(task_id, retry_delay))
            
            return False
            
        finally:
            self.running_tasks.discard(task_id)
    
    async def _schedule_retry(self, task_id: str, delay: int):
        """
        调度重试任务
        
        Args:
            task_id: 任务ID
            delay: 延迟时间（秒）
        """
        await asyncio.sleep(delay)
        
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if task.retry():
                await self.execute_task(task_id)
    
    def schedule_periodic_task(self, task_type: str, interval: str, config: Dict[str, Any] = None):
        """
        调度周期性任务
        
        Args:
            task_type: 任务类型
            interval: 间隔时间 (如: "1h", "30m", "daily")
            config: 任务配置
        """
        def job():
            task_id = self.create_task(task_type, config)
            asyncio.create_task(self.execute_task(task_id))
        
        if interval == "daily":
            schedule.every().day.at("09:00").do(job)
        elif interval.endswith("h"):
            hours = int(interval[:-1])
            schedule.every(hours).hours.do(job)
        elif interval.endswith("m"):
            minutes = int(interval[:-1])
            schedule.every(minutes).minutes.do(job)
        else:
            logger.error(f"Invalid interval format: {interval}")
            return
        
        logger.info(f"Scheduled {task_type} task every {interval}")
    
    async def start_scheduler(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("Scheduler is already running")
            return
        
        self.is_running = True
        logger.info("Scheduler started")
        
        while self.is_running:
            try:
                # 运行调度任务
                schedule.run_pending()
                
                # 处理待执行的任务
                pending_tasks = [
                    task_id for task_id, task in self.tasks.items()
                    if task.status == TaskStatus.PENDING and task_id not in self.running_tasks
                ]
                
                for task_id in pending_tasks:
                    if len(self.running_tasks) < self.default_config["max_concurrent_tasks"]:
                        asyncio.create_task(self.execute_task(task_id))
                
                await asyncio.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error(f"Scheduler error: {e}")
                await asyncio.sleep(60)  # 出错时等待1分钟
    
    def stop_scheduler(self):
        """停止调度器"""
        self.is_running = False
        logger.info("Scheduler stopped")
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict]: 任务状态信息
        """
        if task_id not in self.tasks:
            return None
        
        task = self.tasks[task_id]
        
        return {
            "task_id": task.task_id,
            "task_type": task.task_type,
            "status": task.status.value,
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "retry_count": task.retry_count,
            "max_retries": task.max_retries,
            "error_message": task.error_message,
            "can_retry": task.can_retry()
        }
    
    def get_all_tasks_status(self) -> List[Dict[str, Any]]:
        """获取所有任务状态"""
        return [self.get_task_status(task_id) for task_id in self.tasks.keys()]
