{"app_name": "nm_crawl", "version": "0.1.0", "debug": true, "cdp_url": "", "database": {"url": "sqlite+aiosqlite:///data/financial_products.db", "echo": false, "pool_size": 5, "max_overflow": 10}, "crawler": {"list_wait_time": 10, "detail_wait_time": 8, "detail_delay": 3, "max_scroll_attempts": 5, "max_detail_products": 3, "max_retries": 3, "retry_delay": 300, "max_concurrent_tasks": 2, "headless": true}, "scheduler": {"enable_scheduler": false, "full_crawl_interval": "daily", "list_crawl_interval": "4h", "full_crawl_time": "09:00", "auto_start": false}, "logging": {"level": "DEBUG", "log_directory": "logs/test", "enable_file_logging": true, "max_log_size": "10 MB", "log_retention": "30 days"}, "storage": {"data_directory": "data/test", "backup_enabled": true, "backup_retention_days": 30, "compress_backups": true}}