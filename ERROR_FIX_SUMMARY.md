# 数据库唯一约束错误修复总结

## 🐛 问题描述

在处理历史收益数据时遇到了SQLite唯一约束错误：

```
UNIQUE constraint failed: product_history.product_id, product_history.profit_date
```

### 错误原因
1. **重复数据**: 多个历史文件包含相同产品的相同日期数据
2. **唯一约束**: `product_history`表设置了`(product_id, profit_date)`的复合唯一约束
3. **处理方式**: 原来使用`merge()`方法，但在SQLite中处理复合主键时存在问题

## 🔧 解决方案

### 1. 使用SQLite的`INSERT OR REPLACE`语法

将原来的`merge()`方法替换为SQLite特有的`on_conflict_do_update()`语法：

```python
# 原来的方法（有问题）
history_record = ProductHistory(...)
await session.merge(history_record)

# 新的方法（修复后）
stmt = insert(ProductHistory).values(...)
stmt = stmt.on_conflict_do_update(
    index_elements=['product_id', 'profit_date'],
    set_=dict(
        ten_thousand_profit=stmt.excluded.ten_thousand_profit,
        seven_days_annual_profit=stmt.excluded.seven_days_annual_profit,
        # ... 其他字段
    )
)
await session.execute(stmt)
```

### 2. 统一处理所有表的冲突

为了保持一致性，同时修复了产品列表和产品详情表的冲突处理：

#### 产品列表表
```python
stmt = insert(ProductList).values(...)
stmt = stmt.on_conflict_do_update(
    index_elements=['product_id'],
    set_=dict(
        rip_snm=stmt.excluded.rip_snm,
        prd_rat=stmt.excluded.prd_rat,
        # ... 更新最新数据
    )
)
```

#### 产品详情表
```python
stmt = insert(ProductDetail).values(...)
stmt = stmt.on_conflict_do_update(
    index_elements=['product_id'],
    set_=dict(
        rip_snm=stmt.excluded.rip_snm,
        rate_text=stmt.excluded.rate_text,
        # ... 更新最新数据
    )
)
```

## ✅ 修复结果

### 处理统计
```
✅ 产品列表: 处理了 60 个产品 (去重后20个)
✅ 产品详情: 处理了 15 个详情 (去重后5个)
✅ 历史收益: 处理了 354 条历史记录 (去重后118条)
```

### 数据库最终状态
- **📦 产品列表**: 20个产品
- **📄 产品详情**: 5个产品的详情
- **📊 历史记录**: 118条唯一的历史收益记录

## 🎯 技术要点

### 1. SQLite冲突处理语法
```sql
INSERT INTO table_name (columns...) 
VALUES (values...)
ON CONFLICT (conflict_columns) 
DO UPDATE SET 
    column1 = excluded.column1,
    column2 = excluded.column2;
```

### 2. SQLAlchemy实现
```python
from sqlalchemy.dialects.sqlite import insert

stmt = insert(Table).values(...)
stmt = stmt.on_conflict_do_update(
    index_elements=['unique_column'],
    set_=dict(column=stmt.excluded.column)
)
```

### 3. 复合唯一约束处理
```python
# 对于复合唯一约束 (product_id, profit_date)
stmt = stmt.on_conflict_do_update(
    index_elements=['product_id', 'profit_date'],
    set_=dict(...)
)
```

## 💡 优势

1. **数据完整性**: 确保不会有重复数据
2. **自动更新**: 遇到冲突时自动更新为最新数据
3. **性能优化**: 避免了先查询再插入的两步操作
4. **错误处理**: 优雅处理重复数据，不会中断处理流程

## 🚀 应用场景

这种处理方式特别适合：
- **增量数据更新**: 定期爬取时更新已有数据
- **多源数据合并**: 处理来自不同时间点的相同数据
- **数据修正**: 后续爬取的数据可能更准确，需要覆盖旧数据
- **容错处理**: 避免因重复数据导致的处理中断

## 📋 相关文件修改

1. **`src/nm_crawl/services/database_service.py`**
   - 修改了`save_product_list()`方法
   - 修改了`save_product_detail()`方法  
   - 修改了`save_product_history()`方法
   - 添加了SQLite冲突处理逻辑

2. **导入更新**
   - 添加了`from sqlalchemy.dialects.sqlite import insert`
   - 添加了`from sqlalchemy.exc import IntegrityError`（备用）

## 🎉 总结

通过使用SQLite的`INSERT OR REPLACE`语法，成功解决了唯一约束冲突问题，使系统能够：

1. **优雅处理重复数据**
2. **自动更新最新信息**
3. **保持数据一致性**
4. **提高处理效率**

现在系统可以安全地处理多次爬取的数据，自动去重并保留最新信息！

**✅ 错误修复完成，系统运行正常！**
