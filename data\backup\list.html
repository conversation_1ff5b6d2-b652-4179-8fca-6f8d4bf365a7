<!DOCTYPE html><html lang="en" class="platform-windows baseline-default"><head><meta charset="utf-8">
<title>DevTools</title>
<style>
  @media (prefers-color-scheme: dark) {
    body {
      background-color: rgb(41 42 45);
    }
  }
</style>
<meta http-equiv="Content-Security-Policy" content="object-src 'none'; script-src 'self' https://chrome-devtools-frontend.appspot.com">
<meta name="referrer" content="no-referrer">
<script type="module" src="./entrypoints/devtools_app/devtools_app.js"></script>
<link href="./application_tokens.css" rel="stylesheet">
<link href="./design_system_tokens.css" rel="stylesheet">
<style>:root::highlight(search-highlight){background-color:var(--sys-color-yellow-outline)}
/*# sourceURL=devtools://devtools/bundled/ui/components/highlighting/highlighting.css */</style></head><body class="undocked right" id="-blink-dev-tools" style="">
<style>*{box-sizing:border-box;min-width:0;min-height:0}:root{height:100%;overflow:hidden;interpolate-size:allow-keywords}body{height:100%;width:100%;position:relative;overflow:hidden;margin:0;cursor:default;font-family:var(--default-font-family);font-size:12px;tab-size:4;user-select:none;color:var(--sys-color-on-surface);background:var(--sys-color-cdt-base-container)}:focus{outline-width:0}.monospace{font-family:var(--monospace-font-family);font-size:var(
    --monospace-font-size
  )!important}.source-code{font-family:var(--source-code-font-family);font-size:var(
    --source-code-font-size
  )!important;white-space:pre-wrap;&:not(input)::selection{color:var(--sys-color-on-surface)}}.source-code.breakpoint{white-space:nowrap}.source-code .devtools-link.text-button{max-width:100%;overflow:hidden;text-overflow:ellipsis}img{-webkit-user-drag:none}iframe,
a img{border:none}.fill{position:absolute;inset:0}iframe.fill{width:100%;height:100%}.widget{position:relative;flex:auto;contain:style}.hbox{display:flex;flex-direction:row!important;position:relative}.vbox{display:flex;flex-direction:column!important;position:relative}.view-container > devtools-toolbar{border-bottom:1px solid var(--sys-color-divider)}.flex-auto{flex:auto}.flex-none{flex:none}.flex-centered{display:flex;align-items:center;justify-content:center}.overflow-auto{overflow:auto;background-color:var(--sys-color-cdt-base-container)}iframe.widget{position:absolute;width:100%;height:100%;inset:0}.hidden{display:none!important}.highlighted-search-result{border-radius:1px;background-color:var(--sys-color-yellow-container);outline:1px solid var(--sys-color-yellow-container)}.link{cursor:pointer;text-decoration:underline;color:var(--text-link);outline-offset:2px}button,
input,
select{font-family:inherit;font-size:inherit}select option,
select optgroup,
input{background-color:var(--sys-color-cdt-base-container)}input{color:inherit;&[type='checkbox']{position:relative;outline:none;display:flex;align-items:center;justify-content:center;&:hover::after,
    &:active::before{content:'';height:24px;width:24px;border-radius:var(--sys-shape-corner-full);position:absolute}&:not(.-theme-preserve){accent-color:var(--sys-color-primary-bright);color:var(--sys-color-on-primary)}&:not(:disabled):hover::after{background-color:var(--sys-color-state-hover-on-subtle)}&:not(:disabled):active::before{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:not(:disabled):focus-visible::before{content:'';height:15px;width:15px;border-radius:5px;position:absolute;border:2px solid var(--sys-color-state-focus-ring)}&.small:hover::after,
    &.small:active::before{height:12px;width:12px;border-radius:2px}}}input::placeholder{--override-input-placeholder-color:rgb(0 0 0/54%);color:var(--override-input-placeholder-color)}.theme-with-dark-background input::placeholder,
:host-context(.theme-with-dark-background) input::placeholder{--override-input-placeholder-color:rgb(230 230 230/54%)}.harmony-input:not([type]),
.harmony-input[type='number'],
.harmony-input[type='text']{padding:3px 6px;height:24px;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;&.error-input,
  &:invalid{border-color:var(--sys-color-error)}&:not(.error-input, :invalid):focus{border-color:var(--sys-color-state-focus-ring)}&:not(.error-input, :invalid):hover:not(:focus){background:var(--sys-color-state-hover-on-subtle)}}input[type='radio']{height:17px;width:17px;min-width:17px;border-radius:8px;vertical-align:sub;margin:0 5px 5px 0;accent-color:var(--sys-color-primary-bright);color:var(--sys-color-on-primary);&:focus-visible{outline:var(--sys-size-2) solid var(--sys-color-state-focus-ring)}}@media (forced-colors: active){input[type='radio']{--gradient-start:ButtonFace;--gradient-end:ButtonFace;&:checked{--gradient-start:Highlight;--gradient-end:Highlight}}}input[type='range']{appearance:none;margin:0;padding:0;height:10px;width:88px;outline:none;background:none}input[type='range']::-webkit-slider-thumb,
.-theme-preserve{appearance:none;margin:0;padding:0;border:0;width:12px;height:12px;margin-top:-5px;border-radius:50%;background-color:var(--sys-color-primary)}input[type='range']::-webkit-slider-runnable-track{appearance:none;margin:0;padding:0;width:100%;height:2px;background-color:var(--sys-color-surface-variant)}input[type='range']:focus::-webkit-slider-thumb{box-shadow:0 0 0 2px var(--sys-color-inverse-primary)}input[type='range']:disabled::-webkit-slider-thumb{background-color:var(--sys-color-state-disabled)}@media (forced-colors: active){input[type='range']{forced-color-adjust:none}}.highlighted-search-result.current-search-result{--override-current-search-result-background-color:rgb(255 127 0/80%);border-radius:1px;padding:1px;margin:-1px;background-color:var(--override-current-search-result-background-color)}.dimmed{opacity:60%}.editing{box-shadow:var(--drop-shadow);background-color:var(--sys-color-cdt-base-container);text-overflow:clip!important;padding-left:2px;margin-left:-2px;padding-right:2px;margin-right:-2px;margin-bottom:-1px;padding-bottom:1px;opacity:100%!important}.editing,
.editing *{color:var(
    --sys-color-on-surface
  )!important;text-decoration:none!important}select{appearance:none;user-select:none;height:var(--sys-size-11);border:var(--sys-size-1) solid var(--sys-color-neutral-outline);border-radius:var(--sys-shape-corner-extra-small);color:var(--sys-color-on-surface);font:inherit;margin:0;outline:none;padding:0 var(--sys-size-9) 0 var(--sys-size-5);background-image:var(--combobox-dropdown-arrow);background-color:transparent;background-position:right center;background-repeat:no-repeat;&:disabled{opacity:100%;border-color:transparent;color:var(--sys-color-state-disabled);background-color:var(--sys-color-state-disabled-container);pointer-events:none}&:enabled{&:hover{background-color:var(--sys-color-state-hover-on-subtle)}&:active{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:hover:active{background:var(--combobox-dropdown-arrow),linear-gradient(var(--sys-color-state-hover-on-subtle),var(--sys-color-state-hover-on-subtle)),linear-gradient(var(--sys-color-state-ripple-neutral-on-subtle),var(--sys-color-state-ripple-neutral-on-subtle));background-position:right center;background-repeat:no-repeat}&:focus{outline:var(--sys-size-2) solid var(--sys-color-state-focus-ring);outline-offset:-1px}}}@media (forced-colors: active) and (prefers-color-scheme: light){:root,
  .theme-with-dark-background,
  :host-context(.theme-with-dark-background){--combobox-dropdown-arrow:var(--image-file-arrow-drop-down-light)}}@media (forced-colors: active) and (prefers-color-scheme: dark){:root,
  .theme-with-dark-background,
  :host-context(.theme-with-dark-background){--combobox-dropdown-arrow:var(--image-file-arrow-drop-down-dark)}}.chrome-select-label{margin:0 var(--sys-size-10);flex:none;p p{margin-top:0;color:var(--sys-color-token-subtle)}.reload-warning{margin-left:var(--sys-size-5)}}.settings-select{margin:0}select optgroup,
select option{background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface)}.gray-info-message{text-align:center;font-style:italic;padding:6px;color:var(--sys-color-token-subtle);white-space:nowrap}.empty-state{margin:var(--sys-size-5);display:flex;flex-grow:1;justify-content:center;align-items:center;flex-direction:column;text-align:center;min-height:fit-content;min-width:fit-content;> *{max-width:var(--sys-size-29)}.empty-state-header{font:var(--sys-typescale-headline5);margin-bottom:var(--sys-size-3)}.empty-state-description{font:var(--sys-typescale-body4-regular);color:var(--sys-color-on-surface-subtle);> x-link{white-space:nowrap;margin-left:var(--sys-size-3)}}> devtools-button{margin-top:var(--sys-size-7)}}dt-icon-label{flex:none}.dot::before{content:var(--image-file-empty);width:6px;height:6px;border-radius:50%;outline:1px solid var(--icon-gap-default);left:9px;position:absolute;top:9px;z-index:1}.green::before{background-color:var(--sys-color-green-bright)}.purple::before{background-color:var(--sys-color-purple-bright)}.expandable-inline-button{background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface);cursor:pointer;border-radius:3px}.undisplayable-text,
.expandable-inline-button{border:none;padding:1px 3px;margin:0 2px;font-size:11px;font-family:sans-serif;white-space:nowrap;display:inline-block}.undisplayable-text::after,
.expandable-inline-button::after{content:attr(data-text)}.undisplayable-text{color:var(--sys-color-state-disabled);font-style:italic}.expandable-inline-button:hover,
.expandable-inline-button:focus-visible{background-color:var(--sys-color-state-hover-on-subtle)}.expandable-inline-button:focus-visible{background-color:var(--sys-color-state-focus-highlight)}::selection{background-color:var(--sys-color-state-text-highlight);color:var(--sys-color-state-on-text-highlight)}button.link{border:none;background:none;padding:3px}button.link:focus-visible{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px;border-radius:var(--sys-shape-corner-full)}.data-grid-data-grid-node button.link:focus-visible{border-radius:var(--sys-shape-corner-extra-small);padding:0;margin-top:3px}@media (forced-colors: active){.dimmed,
  select:disabled{opacity:100%}.harmony-input:not([type]),
  .harmony-input[type='number'],
  .harmony-input[type='text']{border:1px solid ButtonText}.harmony-input:not([type]):focus,
  .harmony-input[type='number']:focus,
  .harmony-input[type='text']:focus{border:1px solid Highlight}}input.custom-search-input::-webkit-search-cancel-button{appearance:none;width:16px;height:15px;margin-right:0;opacity:70%;mask-image:var(--image-file-cross-circle-filled);mask-position:center;mask-repeat:no-repeat;mask-size:99%;background-color:var(--icon-default)}input.custom-search-input::-webkit-search-cancel-button:hover{opacity:99%}.spinner::before{display:block;width:var(--dimension,24px);height:var(--dimension,24px);border:var(--override-spinner-size,3px) solid var(--override-spinner-color,var(--sys-color-token-subtle));border-radius:12px;clip:rect(0,var(--clip-size,15px),var(--clip-size,15px),0);content:'';position:absolute;animation:spinner-animation 1s linear infinite;box-sizing:border-box}@keyframes spinner-animation{from{transform:rotate(0)}to{transform:rotate(360deg)}}.adorner-container{display:inline-flex;vertical-align:middle}.adorner-container.hidden{display:none}.adorner-container devtools-adorner{margin-left:3px}:host-context(.theme-with-dark-background) devtools-adorner{--override-adorner-border-color:var(--sys-color-tonal-outline);--override-adorner-active-background-color:var(
    --sys-color-state-riple-neutral-on-subtle
  )}.panel{display:flex;overflow:hidden;position:absolute;inset:0;z-index:0;background-color:var(--sys-color-cdt-base-container)}.panel-sidebar{overflow-x:hidden;background-color:var(--sys-color-cdt-base-container)}iframe.extension{flex:auto;width:100%;height:100%}iframe.panel.extension{display:block;height:100%}@media (forced-colors: active){:root{--legacy-accent-color:Highlight;--legacy-focus-ring-inactive-shadow-color:ButtonText}}devtools-toolbar{& > *{position:relative;display:flex;background-color:transparent;flex:none;align-items:center;justify-content:center;height:var(--toolbar-height);border:none;white-space:pre;overflow:hidden;max-width:100%;color:var(--icon-default);cursor:default;& .devtools-link{color:var(--icon-default)}}.status-buttons{padding:0 var(--sys-size-2);gap:var(--sys-size-2)}& > :not(select){padding:0}& > devtools-issue-counter{margin-top:-4px;padding:0 1px}devtools-adorner.fix-perf-icon{--override-adorner-text-color:transparent;--override-adorner-border-color:transparent;--override-adorner-background-color:transparent}devtools-issue-counter.main-toolbar{margin-left:1px;margin-right:1px}.toolbar-dropdown-arrow{pointer-events:none;flex:none;top:2px}.toolbar-button.dark-text .toolbar-dropdown-arrow{color:var(--sys-color-on-surface)}.toolbar-button{white-space:nowrap;overflow:hidden;min-width:28px;background:transparent;border-radius:0;&[aria-haspopup='true'][aria-expanded='true']{pointer-events:none}}.toolbar-item-search{min-width:5.2em;max-width:300px;flex:1 1 auto;justify-content:start;overflow:revert}.toolbar-text{margin:0 5px;flex:none;color:var(--ui-text)}.toolbar-text:empty{margin:0}.toolbar-has-dropdown{justify-content:space-between;height:var(--sys-size-9);padding:0 var(--sys-size-2) 0 var(--sys-size-4);margin:0 var(--sys-size-2);gap:var(--sys-size-2);border-radius:var(--sys-shape-corner-extra-small);&:hover::after,
    &:active::before{content:'';height:100%;width:100%;border-radius:inherit;position:absolute;top:0;left:0}&:hover::after{background-color:var(--sys-color-state-hover-on-subtle)}&:active::before{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:focus-visible{outline:var(--sys-size-2) solid var(--sys-color-state-focus-ring)}&[disabled]{pointer-events:none;background-color:var(--sys-color-state-disabled-container);color:var(--sys-color-state-disabled)}}.toolbar-has-dropdown-shrinkable{flex-shrink:1}.toolbar-has-dropdown .toolbar-text{margin:0;text-overflow:ellipsis;flex:auto;overflow:hidden;text-align:right}.toolbar-button:not(.toolbar-has-dropdown):focus-visible::before{position:absolute;inset:2px;background-color:var(--sys-color-state-focus-highlight);border-radius:2px;content:'';z-index:-1}.toolbar-glyph{flex:none}.toolbar-button:disabled{opacity:50%}.toolbar-button.copied-to-clipboard::after{content:attr(data-content);position:fixed;margin-top:calc(2 * var(--toolbar-height));padding:3px 5px;color:var(--sys-color-token-subtle);background:var(--sys-color-cdt-base-container);animation:2s fade-out;font-weight:normal;border:1px solid var(--sys-color-divider);border-radius:3px}.toolbar-button.toolbar-state-on .toolbar-glyph{color:var(--icon-toggled)}.toolbar-state-on.toolbar-toggle-with-dot .toolbar-text::after{content:'';position:absolute;bottom:2px;background-color:var(--sys-color-primary-bright);width:4.5px;height:4.5px;border:2px solid var(--override-toolbar-background-color,--sys-color-cdt-base-container);border-radius:50%;right:0}.toolbar-button.toolbar-state-on.toolbar-toggle-with-red-color .toolbar-glyph,
  .toolbar-button.toolbar-state-off.toolbar-default-with-red-color
    .toolbar-glyph{color:var(
      --icon-error
    )!important}.toolbar-button:not(
      .toolbar-has-glyph,
      .toolbar-has-dropdown,
      .largeicon-menu,
      .toolbar-button-secondary
    ){font-weight:bold}.toolbar-button.dark-text .toolbar-text{color:var(
      --sys-color-on-surface
    )!important}.toolbar-button.toolbar-state-on .toolbar-text{color:var(--sys-color-primary)}.toolbar-button.toolbar-state-on:enabled:active .toolbar-text{color:var(--sys-color-primary-bright)}.toolbar-button:enabled:hover:not(:active) .toolbar-glyph{color:var(--sys-color-on-surface)}.toolbar-button:enabled:hover:not(:active) .toolbar-text{color:var(--sys-color-on-surface)}.toolbar-button.toolbar-state-on:enabled:hover:not(:active) .toolbar-glyph{color:var(--sys-color-primary)}.toolbar-button.toolbar-state-on:enabled:hover:not(:active) .toolbar-text{color:var(--sys-color-primary)}& > devtools-checkbox{padding:0 5px 0 0;white-space:unset}& > select{height:var(--sys-size-9);min-width:var(--sys-size-14)}.toolbar-input{box-shadow:inset 0 0 0 2px transparent;box-sizing:border-box;width:120px;height:var(--sys-size-9);padding:0 var(--sys-size-2) 0 var(--sys-size-5);margin:1px 3px;border-radius:100px;min-width:35px;position:relative;&.focused{box-shadow:inset 0 0 0 2px var(--sys-color-state-focus-ring)}&:not(:has(devtools-button:hover), .disabled):hover{background-color:var(--sys-color-state-hover-on-subtle)}&::before{content:'';box-sizing:inherit;height:100%;width:100%;position:absolute;left:0;background:var(--sys-color-cdt-base);z-index:-1}& > devtools-icon{color:var(--sys-color-on-surface-subtle);width:var(--sys-size-8);height:var(--sys-size-8);margin-right:var(--sys-size-3)}&.disabled > devtools-icon{color:var(--sys-color-state-disabled)}}.toolbar-filter .toolbar-input-clear-button{margin-right:var(--sys-size-4)}.toolbar-input-empty .toolbar-input-clear-button{display:none}.toolbar-prompt-proxy{flex:1}.toolbar-input-prompt{flex:1;overflow:hidden;white-space:nowrap;cursor:text;color:var(--sys-color-on-surface)}.toolbar-divider{background-color:var(--sys-color-divider);width:1px;margin:5px 4px;height:16px}.toolbar-spacer{flex:auto}.toolbar-button.emulate-active{background-color:var(--sys-color-surface-variant)}&:not([floating]) > :last-child:not(:first-child, select){flex-shrink:1;justify-content:left}&:not([floating]) > .toolbar-button:last-child:not(:first-child, select){justify-content:left;margin-right:2px}& > .highlight::before{content:'';position:absolute;inset:2px;border-radius:2px;background:var(--sys-color-neutral-container);z-index:-1}& > .highlight:focus-visible{background:var(--sys-color-tonal-container);& > .title{color:var(--sys-color-on-tonal-container)}}devtools-icon.leading-issue-icon{margin:0 7px}@media (forced-colors: active){.toolbar-button:disabled{opacity:100%;color:Graytext}devtools-toolbar > *,
    .toolbar-text{color:ButtonText}.toolbar-button:disabled .toolbar-text{color:Graytext}devtools-toolbar > select:disabled{opacity:100%;color:Graytext}.toolbar-button.toolbar-state-on .toolbar-glyph{forced-color-adjust:none;color:Highlight}.toolbar-button.toolbar-state-on .toolbar-text{forced-color-adjust:none;color:Highlight}.toolbar-button:enabled:hover:not(:active) .toolbar-text,
    .toolbar-button:enabled:focus:not(:active) .toolbar-text{color:HighlightText}.toolbar-button:disabled devtools-icon{color:GrayText}.toolbar-button:disabled .toolbar-glyph{color:GrayText}.toolbar-button:enabled.hover:not(:active) .toolbar-glyph{forced-color-adjust:none;color:Highlight}.toolbar-button:enabled:hover .toolbar-glyph,
    .toolbar-button:enabled:focus .toolbar-glyph,
    .toolbar-button:enabled:hover:not(:active) .toolbar-glyph,
    .toolbar-button:enabled:hover devtools-icon,
    .toolbar-button:enabled:focus devtools-icon{color:HighlightText}.toolbar-input{forced-color-adjust:none;background:canvas;box-shadow:var(--legacy-focus-ring-inactive-shadow)}.toolbar-input.focused,
    .toolbar-input:not(.toolbar-input-empty){forced-color-adjust:none;background:canvas;box-shadow:var(--legacy-focus-ring-active-shadow)}.toolbar-input:hover{box-shadow:var(--legacy-focus-ring-active-shadow)}devtools-toolbar .devtools-link{color:linktext}.toolbar-has-dropdown{forced-color-adjust:none;background:ButtonFace;color:ButtonText}}}@keyframes fade-out{from{opacity:100%}to{opacity:0%}}.webkit-css-property{color:var(
    --webkit-css-property-color,var(--sys-color-token-property-special)
  )}.webkit-html-comment{color:var(--sys-color-token-comment)}.webkit-html-tag{color:var(--sys-color-token-tag)}.webkit-html-tag-name,
.webkit-html-close-tag-name{color:var(--sys-color-token-tag)}.webkit-html-pseudo-element{color:var(--sys-color-token-pseudo-element)}.webkit-html-js-node,
.webkit-html-css-node{color:var(--text-primary);white-space:pre-wrap}.webkit-html-text-node{color:var(--text-primary);unicode-bidi:-webkit-isolate}.webkit-html-entity-value{background-color:rgb(0 0 0/15%);unicode-bidi:-webkit-isolate}.webkit-html-doctype{color:var(--text-secondary)}.webkit-html-attribute-name{color:var(--sys-color-token-attribute);unicode-bidi:-webkit-isolate}.webkit-html-attribute-value{color:var(--sys-color-token-attribute-value);unicode-bidi:-webkit-isolate;word-break:break-all}.devtools-link{color:var(--text-link);text-decoration:underline;outline-offset:2px;.elements-disclosure &{color:var(--text-link)}devtools-icon{vertical-align:baseline;color:var(--sys-color-primary)}:focus .selected & devtools-icon{color:var(--sys-color-tonal-container)}&:focus-visible{outline:var(--sys-size-2) solid var(--sys-color-state-focus-ring);outline-offset:0;border-radius:var(--sys-shape-corner-extra-small)}&.invalid-link{color:var(--text-disabled);text-decoration:none}&:not(.devtools-link-prevent-click, .invalid-link){cursor:pointer}@media (forced-colors: active){&:not(.devtools-link-prevent-click){forced-color-adjust:none;color:linktext}&:focus-visible{background:Highlight;color:HighlightText}}}
/*# sourceURL=devtools://devtools/bundled/ui/legacy/inspectorCommon.css */</style><style>.text-button{margin:2px;height:24px;font-size:12px;font-family:var(--default-font-family);border:1px solid var(--sys-color-tonal-outline);border-radius:12px;padding:0 12px;font-weight:500;color:var(--sys-color-primary);background-color:var(--sys-color-cdt-base-container);flex:none;white-space:nowrap}.text-button:disabled{opacity:38%}.text-button:not(:disabled):focus,
.text-button:not(:disabled):hover,
.text-button:not(:disabled):active{background-color:var(--sys-color-state-hover-on-subtle)}.text-button:not(:disabled, .primary-button):focus-visible{outline:2px solid var(--sys-color-state-focus-ring);color:var(--sys-color-on-primary);background-color:var(--sys-color-cdt-base-container)}.text-button:not(:disabled, .running):focus,
.text-button:not(:disabled, .running):hover,
.text-button:not(:disabled, .running):active{color:var(--sys-color-primary)}.text-button.link-style,
.text-button.link-style:hover,
.text-button.link-style:active{background:none;border:none;outline:none;border-radius:2px;margin:0;padding:0!important;font:inherit;cursor:pointer;height:unset}.text-button.primary-button,
.text-button.primary-button:not(:disabled):focus{background-color:var(--sys-color-primary);border:none;color:var(--sys-color-on-primary)}.text-button.primary-button:not(:disabled):active{background-color:color-mix(in srgb,var(--sys-color-primary),var(--sys-color-state-ripple-primary) 32%);color:var(--sys-color-on-primary)}.text-button.primary-button:not(:disabled):hover{background-color:color-mix(in srgb,var(--sys-color-primary),var(--sys-color-state-hover-on-prominent) 6%);color:var(--sys-color-on-primary)}.text-button.primary-button:not(:disabled):focus-visible{background-color:var(--sys-color-primary);outline-offset:2px;outline:2px solid var(--sys-color-state-focus-ring);color:var(--sys-color-on-primary)}@media (forced-colors: active){.text-button{background-color:ButtonFace;color:ButtonText;border-color:ButtonText}.text-button:disabled{forced-color-adjust:none;opacity:100%;background:ButtonFace;border-color:GrayText;color:GrayText}.text-button:not(:disabled):focus-visible{forced-color-adjust:none;background-color:ButtonFace;color:Highlight!important;border-color:Highlight;outline:2px solid ButtonText;box-shadow:var(--legacy-focus-ring-active-shadow)}.text-button:not(:disabled):hover,
  .text-button:not(:disabled):active{forced-color-adjust:none;background-color:Highlight;color:HighlightText!important;box-shadow:var(--sys-color-primary)}.text-button.primary-button{forced-color-adjust:none;background-color:Highlight;color:HighlightText;border:1px solid Highlight}.text-button.primary-button:not(:disabled):focus-visible{background-color:Highlight;color:HighlightText!important;border-color:ButtonText}.text-button.primary-button:not(:disabled):hover,
  .text-button.primary-button:not(:disabled):active{background-color:HighlightText;color:Highlight!important;border-color:Highlight}}
/*# sourceURL=devtools://devtools/bundled/ui/components/buttons/textButton.css */</style><link href="devtools://theme/colors.css?sets=ui,chrome&amp;version=1756005016995" rel="stylesheet" type="text/css"><div class="widget vbox root-view" spellcheck="false" style="margin-right: 0px; margin-bottom: 0px;"><style>.root-view{background-color:var(--sys-color-cdt-base-container);overflow:hidden;position:absolute!important;inset:0}
/*# sourceURL=devtools://devtools/bundled/ui/legacy/rootView.css */</style><div class="vbox flex-auto split-widget"><div class="widget vbox" slot="sidebar" style="--devtools-window-left: 731px; --devtools-window-right: -0.3333740234375px; --devtools-window-width: 976.3333740234375px; --devtools-window-top: 0px; --devtools-window-bottom: 0px; --devtools-window-height: 932px;"><div class="vbox flex-auto split-widget"><div class="vbox flex-auto tabbed-pane drawer-tabbed-pane" jslog="Drawer" role="complementary" aria-label="Tool drawer" slot="sidebar"><div class="widget vbox flex-auto view-container overflow-auto" tabindex="-1" role="tabpanel" aria-label="What's new panel"><div class="vbox flex-auto panel whats-new" jslog="Panel; context: whats-new; track: resize" aria-label="whats-new"></div></div></div><div class="vbox flex-auto tabbed-pane main-tabbed-pane" slot="main"><div class="widget vbox flex-auto view-container overflow-auto" tabindex="-1" role="tabpanel" aria-label="Elements panel"><div class="widget vbox panel elements" jslog="Panel; context: elements; track: resize" aria-label="elements"><style>#main-content{position:relative;flex:1 1}#elements-content{overflow:auto;padding:2px 0 0;height:100%}.style-panes-wrapper{overflow:hidden scroll;background-color:var(--sys-color-cdt-base-container);& > div:not(:last-child){border-bottom:1px solid var(--sys-color-divider)}}#elements-content:not(.elements-wrap) > div{display:inline-block;min-width:100%}#elements-crumbs{background-color:var(--sys-color-cdt-base-container);border-top:1px solid var(--sys-color-divider);overflow:hidden;width:100%}devtools-adorner-settings-pane{margin-bottom:10px;border-bottom:1px solid var(--sys-color-divider);overflow:auto}devtools-tree-outline{overflow:auto}.axtree-button{position:absolute;top:var(--sys-size-8);right:var(--sys-size-9);background-color:var(--sys-color-cdt-base-container);display:flex;justify-content:center;align-items:center;z-index:1;border-radius:var(--sys-shape-corner-full);box-shadow:var(--sys-elevation-level1)}
/*# sourceURL=devtools://devtools/bundled/panels/elements/elementsPanel.css */</style><div class="vbox flex-auto split-widget"><div class="vbox flex-auto" slot="main"><div id="main-content"><devtools-button role="presentation" tabindex="0" class="axtree-button"></devtools-button><div role="main" aria-label="DOM tree explorer" id="elements-content" tabindex="-1" class="elements-wrap"><div></div></div></div><div id="elements-crumbs"><devtools-elements-breadcrumbs></devtools-elements-breadcrumbs></div></div><div class="vbox flex-auto tabbed-pane" slot="sidebar"><div class="widget vbox flex-auto view-container overflow-auto" tabindex="-1" role="tabpanel" aria-label="Styles panel"><div class="widget vbox flex-auto"><div class="vbox flex-auto split-widget"><div class="widget vbox style-panes-wrapper" jslog="Pane; context: styles; track: resize" slot="main" style="height: 678px;"><div class="vbox flex-auto flex-none"></div><div class="vbox flex-auto flex-none"></div></div></div></div></div></div></div></div></div></div></div></div><div class="widget vbox" slot="main"><div class="vbox flex-auto device-mode-view"><div class="vbox flex-auto"></div></div></div></div></div><div role="alert" aria-atomic="true" style="position: absolute; left: -999em; width: 100em; overflow: hidden;">scrollTo, suggestion 4 of 18</div><div class="editor-tooltip-host"></div></body></html>