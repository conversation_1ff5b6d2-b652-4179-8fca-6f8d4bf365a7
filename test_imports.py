#!/usr/bin/env python3

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("开始测试导入...")

try:
    print("1. 测试模型导入...")
    from src.nm_crawl.models.financial_models import ProductList, ProductDetail, HistoryProfits
    print("✅ 模型导入成功")
    
    print("2. 测试数据库连接导入...")
    from src.nm_crawl.database.connection import init_database
    print("✅ 数据库连接导入成功")
    
    print("3. 测试数据库服务导入...")
    from src.nm_crawl.services.database_service import CmbDatabaseService
    print("✅ 数据库服务导入成功")
    
    print("🎉 所有导入成功！")
    
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
