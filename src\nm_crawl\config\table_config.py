"""
表格数据配置管理器
用于定义不同chart_api的表结构、字段映射和数据处理规则
"""

from typing import Dict, List, Any, Optional, Callable, Type
from dataclasses import dataclass, field
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Boolean, JSON, func
from sqlalchemy.ext.declarative import declarative_base

from ..models.financial_models import Base, HistoryProfits, HistoryNetValue, HistoryPerformance


@dataclass
class FieldMapping:
    """字段映射配置"""
    source_field: str  # 源数据字段名
    target_field: str  # 目标表字段名
    data_type: str = "string"  # 数据类型: string, integer, float, boolean, json
    required: bool = False  # 是否必填
    default_value: Any = None  # 默认值
    transformer: Optional[Callable] = None  # 数据转换函数


@dataclass
class TableConfig:
    """表格配置"""
    api_name: str  # chart_api名称，如 "get-history-profit"
    table_name: str  # 数据库表名
    model_class: Type  # SQLAlchemy模型类
    unique_fields: List[str] = field(default_factory=list)  # 唯一性字段组合（用于去重）
    field_mappings: List[FieldMapping] = field(default_factory=list)  # 字段映射列表
    data_extractor: Optional[Callable] = None  # 自定义数据提取函数
    validator: Optional[Callable] = None  # 数据验证函数


class TableConfigManager:
    """表格配置管理器"""
    
    def __init__(self):
        self._configs: Dict[str, TableConfig] = {}
        self._init_default_configs()
    
    def _init_default_configs(self):
        """初始化默认配置"""
        # 历史收益配置
        profit_config = TableConfig(
            api_name="get-history-profit",
            table_name="history_profits",
            model_class=HistoryProfits,
            unique_fields=["product_id", "profit_date"],
            field_mappings=[
                FieldMapping("date", "profit_date", required=True),
                FieldMapping("tenThousandProfit", "ten_thousand_profit"),
                FieldMapping("sevenDaysAnnualProfit", "seven_days_annual_profit"),
            ]
        )

        # 历史净值配置
        netvalue_config = TableConfig(
            api_name="get-history-net-value",
            table_name="history_netvalue",
            model_class=HistoryNetValue,
            unique_fields=["product_id", "net_value_date"],
            field_mappings=[
                FieldMapping("date", "net_value_date", required=True),
                FieldMapping("unitNetValue", "unit_net_value"),
                FieldMapping("totalNetValue", "total_net_value"),
                FieldMapping("netValueChange", "net_value_change"),
                FieldMapping("showProvision", "show_provision", data_type="boolean", default_value=False),
            ]
        )

        # 历史业绩配置 - 新增
        performance_config = TableConfig(
            api_name="get-history-performance",
            table_name="history_performance",
            model_class=HistoryPerformance,
            unique_fields=["product_id", "prf_typ", "time_interval"],
            field_mappings=[
                FieldMapping("prfTyp", "prf_typ", required=True),
                FieldMapping("timeInterval", "time_interval", required=True),
                FieldMapping("netValueChange", "net_value_change"),
                FieldMapping("yeaYld", "yea_yld"),
            ],
            validator=None  # 暂时不使用验证器，避免循环引用
        )

        self.register_config(profit_config)
        self.register_config(netvalue_config)
        self.register_config(performance_config)
    
    def register_config(self, config: TableConfig):
        """注册表格配置"""
        self._configs[config.api_name] = config
    
    def get_config(self, api_name: str) -> Optional[TableConfig]:
        """获取表格配置"""
        return self._configs.get(api_name)
    
    def get_all_configs(self) -> Dict[str, TableConfig]:
        """获取所有配置"""
        return self._configs.copy()
    
    def get_api_names(self) -> List[str]:
        """获取所有API名称"""
        return list(self._configs.keys())
    
    def is_supported_api(self, api_name: str) -> bool:
        """检查是否支持指定的API"""
        return api_name in self._configs


# 全局配置管理器实例
table_config_manager = TableConfigManager()


def get_table_config(api_name: str) -> Optional[TableConfig]:
    """获取表格配置的便捷函数"""
    return table_config_manager.get_config(api_name)


def register_table_config(config: TableConfig):
    """注册表格配置的便捷函数"""
    table_config_manager.register_config(config)


# 数据转换函数示例
def convert_to_boolean(value: Any) -> bool:
    """转换为布尔值"""
    if isinstance(value, bool):
        return value
    if isinstance(value, str):
        return value.lower() in ('true', '1', 'yes', 'on')
    if isinstance(value, (int, float)):
        return bool(value)
    return False


def convert_to_float(value: Any) -> Optional[float]:
    """转换为浮点数"""
    if value is None or value == '':
        return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return None


def convert_to_int(value: Any) -> Optional[int]:
    """转换为整数"""
    if value is None or value == '':
        return None
    try:
        return int(float(value))
    except (ValueError, TypeError):
        return None


# 数据验证函数示例
def validate_date_format(date_str: str) -> bool:
    """验证日期格式"""
    if not date_str:
        return False
    try:
        datetime.strptime(date_str, '%Y-%m-%d')
        return True
    except ValueError:
        return False


def validate_profit_data(data: Dict[str, Any]) -> bool:
    """验证收益数据"""
    required_fields = ['date']
    return all(field in data and data[field] for field in required_fields)


def validate_netvalue_data(data: Dict[str, Any]) -> bool:
    """验证净值数据"""
    required_fields = ['date']
    return all(field in data and data[field] for field in required_fields)


def validate_performance_data(data: Dict[str, Any]) -> bool:
    """验证业绩数据"""
    required_fields = ['prfTyp', 'timeInterval']
    return all(field in data and data[field] for field in required_fields)
