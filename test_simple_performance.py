"""
简单测试 get-history-performance 功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    print("开始导入模块...")
    
    from src.nm_crawl.config.table_config import get_table_config, table_config_manager
    print("✓ 成功导入 table_config")
    
    from src.nm_crawl.models.financial_models import HistoryPerformance
    print("✓ 成功导入 HistoryPerformance 模型")
    
    # 检查配置
    performance_config = get_table_config("get-history-performance")
    if performance_config:
        print(f"✓ 找到业绩数据配置: {performance_config.api_name}")
        print(f"  - 表名: {performance_config.table_name}")
        print(f"  - 模型类: {performance_config.model_class.__name__}")
        print(f"  - 唯一字段: {performance_config.unique_fields}")
        print(f"  - 字段映射数量: {len(performance_config.field_mappings)}")
        
        for mapping in performance_config.field_mappings:
            print(f"    {mapping.source_field} -> {mapping.target_field} (required: {mapping.required})")
    else:
        print("✗ 未找到业绩数据配置")
    
    # 检查所有支持的API
    all_apis = table_config_manager.get_api_names()
    print(f"✓ 支持的API列表: {all_apis}")
    
    print("\n🎉 基本功能测试通过！")
    
except Exception as e:
    print(f"✗ 测试失败: {e}")
    import traceback
    traceback.print_exc()
