#!/usr/bin/env python3
"""
测试新的数据库表结构
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.database.connection import init_database
from src.nm_crawl.services.database_service import CmbDatabaseService
from loguru import logger


async def test_new_structure():
    """测试新的数据库结构"""
    
    print("🔧 测试新的数据库表结构")
    print("=" * 50)
    
    try:
        # 初始化数据库
        print("1. 初始化数据库...")
        await init_database()
        print("✅ 数据库初始化成功")
        
        # 创建数据库服务
        print("2. 创建数据库服务...")
        db_service = CmbDatabaseService()
        print("✅ 数据库服务创建成功")
        
        # 测试数据处理
        print("3. 处理数据到数据库...")
        from src.nm_crawl.services.data_processor import DataProcessor
        
        processor = DataProcessor("data")
        results = await processor.process_all_data()
        
        print(f"✅ 数据处理完成:")
        print(f"   - 产品列表: {results['products']} 个")
        print(f"   - 产品详情: {results['details']} 个")
        print(f"   - 历史记录: {results['history']} 条")
        
        # 获取统计信息
        print("4. 获取数据库统计...")
        stats = await processor.get_database_stats()
        print(f"✅ 数据库统计:")
        print(f"   - 总产品数: {stats['products']}")
        print(f"   - 总历史记录: {stats['history_records']}")
        
        print("\n🎉 新的数据库结构测试成功！")
        print("\n📊 表结构信息:")
        print("   - product_list: 产品列表表 (无cmb_前缀)")
        print("   - product_details: 产品详情表 (精简字段)")
        print("   - product_history: 历史收益表 (无cmb_前缀)")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    # 设置简单日志
    logger.remove()
    logger.add(sys.stdout, level="ERROR", format="{time:HH:mm:ss} | {level} | {message}")
    
    success = await test_new_structure()
    
    if success:
        print("\n✅ 所有测试通过！")
        print("💡 现在可以使用 python query_database.py 查看数据")
    else:
        print("\n❌ 测试失败")


if __name__ == "__main__":
    asyncio.run(main())
