#!/usr/bin/env python3
"""
Test the updated crawler
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.crawlers.list_crawler import FinancialProductListCrawler
from src.nm_crawl.utils.logger import setup_logging
from loguru import logger


async def test_updated_crawler():
    """测试更新后的爬虫"""
    
    logger.info("测试更新后的列表爬虫")
    
    # 初始化爬虫
    crawler = FinancialProductListCrawler("data")
    
    try:
        # 爬取产品列表
        products = await crawler.crawl_product_list(wait_time=10, max_scroll_attempts=2)
        
        if products:
            logger.info(f"✓ 成功爬取 {len(products)} 个产品")
            
            # 显示前几个产品
            for i, product in enumerate(products[:5], 1):
                name = product.get('ripSnm', '未知产品')
                rate = product.get('prdRat', 'N/A')
                code = product.get('ripCod', 'N/A')
                logger.info(f"  {i}. {name} - {rate} (代码: {code})")
            
            # 获取产品代码用于详情爬取
            product_codes = await crawler.get_product_codes()
            logger.info(f"获取到 {len(product_codes)} 个产品代码")
            
            return True
        else:
            logger.error("✗ 没有爬取到任何产品")
            return False
            
    except Exception as e:
        logger.error(f"爬取失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    # 设置日志
    setup_logging(level="INFO", enable_file_logging=False)
    
    logger.info("开始测试更新后的爬虫")
    
    success = await test_updated_crawler()
    
    if success:
        logger.info("✓ 测试成功！爬虫已修复")
        print("\n现在您可以:")
        print("1. 使用 python run_crawler.py 运行完整的爬虫系统")
        print("2. 使用 nm-crawl crawl-list 命令行工具")
        print("3. 集成到您的应用中")
    else:
        logger.error("✗ 测试失败")


if __name__ == "__main__":
    asyncio.run(main())
