"""
Main entry point for the financial product crawler
"""

import asyncio
import sys
from typing import Optional
import click
from loguru import logger

from .config.settings import get_config, load_config, ConfigManager
from .database.connection import init_database, close_database
from .utils.logger import setup_logging, get_monitor
from .utils.scheduler import CrawlScheduler
from .crawlers.list_crawler import FinancialProductListCrawler
from .crawlers.detail_crawler import FinancialProductDetailCrawler
from .services.data_processor import DataProcessor


class CrawlerApp:
    """爬虫应用主类"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化应用
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = None
        self.scheduler = None
        self.monitor = None
        
    async def initialize(self):
        """初始化应用"""
        # 加载配置
        self.config = load_config(self.config_file)
        
        # 设置日志
        setup_logging(
            log_directory=self.config.logging.log_directory,
            level=self.config.logging.level,
            enable_file_logging=self.config.logging.enable_file_logging
        )
        
        # 初始化数据库
        await init_database(self.config.database.url)
        
        # 初始化监控器
        self.monitor = get_monitor(self.config.logging.log_directory)
        
        # 初始化调度器
        self.scheduler = CrawlScheduler(self.config.storage.data_directory)
        
        logger.info(f"Application initialized with config: {self.config_file}")
    
    async def cleanup(self):
        """清理资源"""
        if self.scheduler:
            self.scheduler.stop_scheduler()
        
        await close_database()
        logger.info("Application cleanup completed")
    
    async def crawl_list(self, wait_time: int = None, max_scroll_attempts: int = None) -> dict:
        """
        爬取产品列表
        
        Args:
            wait_time: 等待时间
            max_scroll_attempts: 最大滚动尝试次数
            
        Returns:
            dict: 爬取结果
        """
        session_id = f"list_{int(asyncio.get_event_loop().time())}"
        self.monitor.start_crawl_session(session_id, "list")
        
        try:
            cdp_url = "http://localhost:14651"
            crawler = FinancialProductListCrawler(cdp_url=cdp_url, save_directory=self.config.storage.data_directory)
            
            wait_time = wait_time or self.config.crawler.list_wait_time
            max_scroll_attempts = max_scroll_attempts or self.config.crawler.max_scroll_attempts
            
            products = await crawler.crawl_product_list(wait_time, max_scroll_attempts)
            
            result = {
                "products_count": len(products),
                "products": products,
                "session_id": session_id
            }
            
            self.monitor.end_crawl_session(session_id, True, result)
            return result
            
        except Exception as e:
            self.monitor.end_crawl_session(session_id, False, error=str(e))
            raise
    
    async def crawl_details(self, product_codes: list, wait_time: int = None, delay: int = None) -> dict:
        """
        爬取产品详情
        
        Args:
            product_codes: 产品代码列表
            wait_time: 等待时间
            delay: 请求间延迟
            
        Returns:
            dict: 爬取结果
        """
        session_id = f"detail_{int(asyncio.get_event_loop().time())}"
        self.monitor.start_crawl_session(session_id, "detail")
        
        try:
            crawler = FinancialProductDetailCrawler(cdp_url="http://localhost:14651",save_directory=self.config.storage.data_directory)
            
            wait_time = wait_time or self.config.crawler.detail_wait_time
            delay = delay or self.config.crawler.detail_delay
            
            results = await crawler.crawl_multiple_products(product_codes, wait_time, delay)
            
            result = {
                "details_count": len(results.get("details", {})),
                "history_count": len(results.get("history", {})),
                "success_count": results.get("success_count", 0),
                "failed_count": results.get("failed_count", 0),
                "session_id": session_id
            }
            
            self.monitor.end_crawl_session(session_id, True, result)
            return result
            
        except Exception as e:
            self.monitor.end_crawl_session(session_id, False, error=str(e))
            raise
    
    async def crawl_full(self, max_products: int = None) -> dict:
        """
        完整爬取（列表+详情）
        
        Args:
            max_products: 最大产品数量
            
        Returns:
            dict: 爬取结果
        """
        session_id = f"full_{int(asyncio.get_event_loop().time())}"
        self.monitor.start_crawl_session(session_id, "full")
        
        try:
            # 爬取列表
            list_crawler = FinancialProductListCrawler(cdp_url="http://localhost:14651", save_directory=self.config.storage.data_directory)
            products = await list_crawler.crawl_product_list(
                self.config.crawler.list_wait_time,
                self.config.crawler.max_scroll_attempts
            )
            
            if not products:
                raise ValueError("No products found in list crawling")
            
            self.monitor.log_progress(session_id, {"stage": "list_completed", "products_count": len(products)})
            
            # 获取产品代码
            product_codes = []
            for product in products:
                rip_cod = product.get("ripCod")
                saa_cod = product.get("saaCod")
                if rip_cod and saa_cod:
                    product_codes.append((rip_cod, saa_cod))
            
            # 限制详情爬取数量
            max_products = max_products or self.config.crawler.max_detail_products
            if len(product_codes) > max_products:
                product_codes = product_codes[:max_products]
                self.monitor.log_warning(session_id, f"Limited detail crawling to {max_products} products")
            
            # 爬取详情
            detail_crawler = FinancialProductDetailCrawler(cdp_url="http://localhost:14651", save_directory = self.config.storage.data_directory)
            detail_results = await detail_crawler.crawl_multiple_products(
                product_codes,
                self.config.crawler.detail_wait_time,
                self.config.crawler.detail_delay
            )
            
            self.monitor.log_progress(session_id, {
                "stage": "details_completed",
                "details_count": len(detail_results.get("details", {})),
                "history_count": len(detail_results.get("history", {}))
            })
            
            # 保存数据到数据库
            data_processor = DataProcessor(self.config.storage.data_directory)
            storage_result = await data_processor.process_all_data()
            
            result = {
                "products_count": len(products),
                "details_count": len(detail_results.get("details", {})),
                "history_count": len(detail_results.get("history", {})),
                "storage_result": storage_result,
                "session_id": session_id
            }
            
            self.monitor.end_crawl_session(session_id, True, result)
            return result
            
        except Exception as e:
            self.monitor.end_crawl_session(session_id, False, error=str(e))
            raise
    
    async def start_scheduler(self):
        """启动调度器"""
        if not self.config.scheduler.enable_scheduler:
            logger.warning("Scheduler is disabled in configuration")
            return
        
        # 配置定时任务
        if self.config.scheduler.full_crawl_interval:
            self.scheduler.schedule_periodic_task(
                "full",
                self.config.scheduler.full_crawl_interval,
                {"max_detail_products": self.config.crawler.max_detail_products}
            )
        
        if self.config.scheduler.list_crawl_interval:
            self.scheduler.schedule_periodic_task(
                "list",
                self.config.scheduler.list_crawl_interval
            )
        
        logger.info("Starting scheduler...")
        await self.scheduler.start_scheduler()
    
    def get_status(self) -> dict:
        """获取应用状态"""
        metrics = self.monitor.get_metrics() if self.monitor else {}
        health = self.monitor.check_health() if self.monitor else {"is_healthy": False}
        
        return {
            "config": {
                "database_url": self.config.database.url,
                "data_directory": self.config.storage.data_directory,
                "scheduler_enabled": self.config.scheduler.enable_scheduler,
                "log_level": self.config.logging.level
            },
            "metrics": metrics,
            "health": health
        }


# CLI命令
@click.group()
@click.option('--config', '-c', default='config.json', help='Configuration file path')
@click.pass_context
def cli(ctx, config):
    """Financial Product Crawler CLI"""
    ctx.ensure_object(dict)
    ctx.obj['config_file'] = config


@cli.command()
@click.option('--wait-time', '-w', type=int, help='Wait time in seconds')
@click.option('--max-scroll', '-s', type=int, help='Maximum scroll attempts')
@click.pass_context
def crawl_list(ctx, wait_time, max_scroll):
    """Crawl product list"""
    async def run():
        app = CrawlerApp(ctx.obj['config_file'])
        await app.initialize()
        try:
            result = await app.crawl_list(wait_time, max_scroll)
            click.echo(f"Crawled {result['products_count']} products")
        finally:
            await app.cleanup()
    
    asyncio.run(run())


@cli.command()
@click.option('--max-products', '-m', type=int, help='Maximum number of products to crawl details')
@click.pass_context
def crawl_full(ctx, max_products):
    """Crawl full data (list + details)"""
    async def run():
        app = CrawlerApp(ctx.obj['config_file'])
        await app.initialize()
        try:
            result = await app.crawl_full(max_products)
            click.echo(f"Crawled {result['products_count']} products, {result['details_count']} details")
        finally:
            await app.cleanup()
    
    asyncio.run(run())


@cli.command()
@click.pass_context
def start_scheduler(ctx):
    """Start the scheduler"""
    async def run():
        app = CrawlerApp(ctx.obj['config_file'])
        await app.initialize()
        try:
            await app.start_scheduler()
        except KeyboardInterrupt:
            logger.info("Scheduler stopped by user")
        finally:
            await app.cleanup()
    
    asyncio.run(run())


@cli.command()
@click.pass_context
def status(ctx):
    """Show application status"""
    async def run():
        app = CrawlerApp(ctx.obj['config_file'])
        await app.initialize()
        try:
            status_info = app.get_status()
            click.echo("=== Application Status ===")
            click.echo(f"Database: {status_info['config']['database_url']}")
            click.echo(f"Data Directory: {status_info['config']['data_directory']}")
            click.echo(f"Scheduler: {'Enabled' if status_info['config']['scheduler_enabled'] else 'Disabled'}")
            click.echo(f"Health: {'Healthy' if status_info['health']['is_healthy'] else 'Unhealthy'}")
            
            metrics = status_info['metrics']
            click.echo(f"\n=== Metrics ===")
            click.echo(f"Total Crawls: {metrics.get('total_crawls', 0)}")
            click.echo(f"Success Rate: {metrics.get('success_rate', 0)}%")
            click.echo(f"Total Products: {metrics.get('total_products', 0)}")
            click.echo(f"Last Crawl: {metrics.get('last_crawl_time', 'Never')}")
        finally:
            await app.cleanup()
    
    asyncio.run(run())


@cli.command()
@click.pass_context
def init_config(ctx):
    """Initialize default configuration"""
    config_manager = ConfigManager(ctx.obj['config_file'])
    config_file = config_manager.create_default_config()
    env_file = config_manager.create_example_env()
    
    click.echo(f"Created default configuration: {config_file}")
    click.echo(f"Created example environment file: {env_file}")


if __name__ == '__main__':
    cli()
