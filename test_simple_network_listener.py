#!/usr/bin/env python3
"""
Simple test for network listener crawler
"""

import asyncio
import json
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from crawl4ai import AsyncWebCrawler
from loguru import logger


async def test_simple_network_listener():
    """测试简单的网络监听功能"""
    
    # 招商银行理财产品列表页URL
    list_page_url = (
        "https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html"
        "?_u=24&PrdTyp=A&behavior_entryid=LC0JGQ001&behavior_pageid=459C25E0"
        "&behavior_slotid=&appflag=1&_urlrefer=PLATFORM&DeviceType=E"
        "&Version=13.3.3&SystemVersion=11"
    )
    
    logger.info(f"测试网络监听: {list_page_url}")
    
    # 确保debug目录存在
    os.makedirs("debug", exist_ok=True)
    
    try:
        async with <PERSON>ync<PERSON>eb<PERSON>raw<PERSON>(verbose=True) as crawler:
            # 配置浏览器
            browser_config = {
                "headless": False,  # 显示浏览器
                "viewport": {"width": 414, "height": 896},
                "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
                "extra_args": [
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-blink-features=AutomationControlled"
                ]
            }
            
            # JavaScript代码用于监听网络请求
            js_code = """
            console.log('开始网络监听');
            
            // 存储捕获的请求
            const capturedRequests = [];
            const targetApis = ['cashlistnew', 'financelist', 'productlist'];
            
            // 重写fetch
            const originalFetch = window.fetch;
            window.fetch = function(url, options = {}) {
                console.log('Fetch请求:', url);
                
                return originalFetch(url, options).then(async response => {
                    console.log('Fetch响应:', url, response.status);
                    
                    // 检查是否为目标API
                    const isTarget = targetApis.some(api => url.toLowerCase().includes(api));
                    
                    if (isTarget && response.status === 200) {
                        try {
                            const clonedResponse = response.clone();
                            const text = await clonedResponse.text();
                            console.log('捕获到目标响应:', url);
                            console.log('响应长度:', text.length);
                            
                            capturedRequests.push({
                                url: url,
                                status: response.status,
                                response: text,
                                timestamp: new Date().toISOString(),
                                method: 'fetch'
                            });
                        } catch (e) {
                            console.error('处理响应失败:', e);
                        }
                    }
                    return response;
                });
            };
            
            // 重写XMLHttpRequest
            const originalXHR = window.XMLHttpRequest;
            window.XMLHttpRequest = function() {
                const xhr = new originalXHR();
                const originalOpen = xhr.open;
                const originalSend = xhr.send;
                
                xhr.open = function(method, url, ...args) {
                    this._method = method;
                    this._url = url;
                    console.log('XHR请求:', method, url);
                    return originalOpen.apply(this, [method, url, ...args]);
                };
                
                xhr.send = function(data) {
                    const self = this;
                    this.addEventListener('readystatechange', function() {
                        if (this.readyState === 4) {
                            console.log('XHR响应:', self._method, self._url, this.status);
                            
                            // 检查是否为目标API
                            const isTarget = targetApis.some(api => 
                                self._url && self._url.toLowerCase().includes(api)
                            );
                            
                            if (isTarget && this.status === 200) {
                                console.log('捕获到目标XHR响应:', self._url);
                                console.log('响应长度:', this.responseText.length);
                                
                                capturedRequests.push({
                                    url: self._url,
                                    method: self._method,
                                    status: this.status,
                                    response: this.responseText,
                                    timestamp: new Date().toISOString(),
                                    type: 'xhr'
                                });
                            }
                        }
                    });
                    return originalSend.apply(this, [data]);
                };
                
                return xhr;
            };
            
            // 等待页面加载
            await new Promise(resolve => setTimeout(resolve, 8000));
            console.log('页面加载完成，开始用户行为模拟');
            
            // 模拟用户滚动
            for (let i = 0; i < 3; i++) {
                console.log(`滚动 ${i + 1}`);
                window.scrollTo(0, document.body.scrollHeight);
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                window.scrollTo(0, 0);
                await new Promise(resolve => setTimeout(resolve, 1500));
            }
            
            // 尝试点击标签
            const tabs = document.querySelectorAll('.swiper-slide, .tab-item, button, [role="tab"]');
            console.log(`找到 ${tabs.length} 个可点击元素`);
            
            for (let i = 0; i < Math.min(tabs.length, 5); i++) {
                try {
                    const tab = tabs[i];
                    const text = tab.textContent || tab.innerText || '';
                    console.log(`点击元素 ${i + 1}: ${text}`);
                    
                    tab.click();
                    await new Promise(resolve => setTimeout(resolve, 4000));
                } catch (e) {
                    console.log(`点击失败 ${i + 1}:`, e);
                }
            }
            
            // 最后等待
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            console.log(`最终捕获的请求数量: ${capturedRequests.length}`);
            capturedRequests.forEach((req, index) => {
                console.log(`请求 ${index + 1}: ${req.url} - ${req.status}`);
            });
            
            return capturedRequests;
            """
            
            logger.info("开始爬取...")
            
            # 执行爬取
            result = await crawler.arun(
                url=list_page_url,
                wait_for=40,  # 等待40秒
                browser_config=browser_config,
                js_code=js_code
            )
            
            logger.info("爬取完成，分析结果...")
            
            # 保存调试信息
            debug_file = f"debug/network_listener_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            debug_data = {
                "timestamp": datetime.now().isoformat(),
                "url": list_page_url,
                "html_length": len(result.html) if result.html else 0,
                "markdown_length": len(result.markdown) if result.markdown else 0,
                "js_execution_result": getattr(result, 'js_execution_result', None)
            }
            
            with open(debug_file, 'w', encoding='utf-8') as f:
                json.dump(debug_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"调试信息已保存到: {debug_file}")
            
            # 分析结果
            captured_requests = getattr(result, 'js_execution_result', [])
            
            if captured_requests:
                logger.info(f"捕获到 {len(captured_requests)} 个请求")
                
                # 分析捕获的请求
                products = []
                for req in captured_requests:
                    if req.get('response') and req.get('status') == 200:
                        try:
                            data = json.loads(req['response'])
                            if data.get("sysCode") in [200, 1014] and "bizResult" in data:
                                biz_result = data["bizResult"]
                                if biz_result.get("code") == 200 and "data" in biz_result:
                                    prd_list = biz_result["data"].get("prdList", [])
                                    if prd_list:
                                        products.extend(prd_list)
                                        logger.info(f"从请求中提取到 {len(prd_list)} 个产品")
                        except Exception as e:
                            logger.error(f"解析响应失败: {e}")
                
                logger.info(f"总共提取到 {len(products)} 个产品")
                
                if products:
                    # 显示前几个产品
                    for i, product in enumerate(products[:3], 1):
                        logger.info(f"产品 {i}: {product.get('ripSnm', '未知')} - {product.get('prdRat', 'N/A')}")
                    
                    # 保存产品数据
                    products_file = f"debug/products_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    with open(products_file, 'w', encoding='utf-8') as f:
                        json.dump({
                            "products": products,
                            "total_count": len(products),
                            "crawl_time": datetime.now().isoformat(),
                            "source": "network_listener_test"
                        }, f, ensure_ascii=False, indent=2)
                    
                    logger.info(f"产品数据已保存到: {products_file}")
                    
                    return products
                else:
                    logger.warning("没有提取到任何产品数据")
                    return []
            else:
                logger.error("没有捕获到任何网络请求")
                return []
                
    except Exception as e:
        logger.error(f"网络监听测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []


async def main():
    """主函数"""
    logger.info("开始简单网络监听测试")
    
    try:
        products = await test_simple_network_listener()
        
        if products:
            logger.info(f"✓ 成功获取到 {len(products)} 个产品")
            print("\n网络监听爬虫测试成功！")
            print("现在可以集成到主项目中")
        else:
            logger.warning("✗ 没有获取到任何产品")
            print("\n可能的原因:")
            print("1. 网站结构发生变化")
            print("2. JavaScript执行时间不够")
            print("3. 需要更复杂的用户行为模拟")
            print("4. 网络请求被拦截或过滤")
            print("\n请检查 debug/ 目录下的调试文件")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 设置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:HH:mm:ss} | {level} | {message}")
    
    # 运行测试
    asyncio.run(main())
