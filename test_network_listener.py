#!/usr/bin/env python3
"""
Test the network listener crawler
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.crawlers.list_crawler import FinancialProductListCrawler
from src.nm_crawl.utils.logger import setup_logging
from loguru import logger


async def test_updated_list_crawler():
    """测试更新后的列表爬虫"""
    
    logger.info("测试更新后的列表爬虫")
    
    # 初始化爬虫
    crawler = FinancialProductListCrawler("data")
    
    try:
        # 爬取产品列表
        products = await crawler.crawl_product_list(wait_time=25, max_scroll_attempts=3)
        
        if products:
            logger.info(f"✓ 成功爬取 {len(products)} 个产品")
            
            # 显示前几个产品
            for i, product in enumerate(products[:5], 1):
                name = product.get('ripSnm', '未知产品')
                rate = product.get('prdRat', 'N/A')
                code = product.get('ripCod', 'N/A')
                logger.info(f"  {i}. {name} - {rate} (代码: {code})")
            
            # 获取产品代码用于详情爬取
            product_codes = await crawler.get_product_codes()
            logger.info(f"获取到 {len(product_codes)} 个产品代码")
            
            return True
        else:
            logger.error("✗ 没有爬取到任何产品")
            return False
            
    except Exception as e:
        logger.error(f"爬取失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    # 设置日志
    setup_logging(level="INFO", enable_file_logging=False)
    
    logger.info("开始测试网络监听爬虫")
    
    print("选择测试方式:")
    print("1. 测试独立的网络监听爬虫")
    print("2. 测试更新后的列表爬虫")
    print("3. 两个都测试")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    success = False
    
    if choice == "1":
        success = False
    elif choice == "2":
        success = await test_updated_list_crawler()
    elif choice == "3":
        logger.info("=" * 50)
        logger.info("测试1: 独立的网络监听爬虫")
        success1 = False
        
        logger.info("=" * 50)
        logger.info("测试2: 更新后的列表爬虫")
        success2 = await test_updated_list_crawler()
        
        success = success1 or success2
    else:
        logger.error("无效选择")
        return
    
    if success:
        logger.info("✓ 测试成功！网络监听爬虫工作正常")
        print("\n现在您可以:")
        print("1. 使用 python run_crawler.py 运行完整的爬虫系统")
        print("2. 使用 nm-crawl crawl-list 命令行工具")
        print("3. 集成到您的应用中")
        print("4. 检查 data/ 目录下的爬取结果")
    else:
        logger.error("✗ 测试失败")
        print("\n可能的原因:")
        print("1. crawl4ai版本不支持网络监听功能")
        print("2. 浏览器配置问题")
        print("3. 网站结构发生变化")
        print("4. 网络连接问题")
        
        print("\n建议:")
        print("1. 检查 data/requests/ 目录下的网络请求日志")
        print("2. 尝试调整等待时间")
        print("3. 检查浏览器是否正常启动")


if __name__ == "__main__":
    asyncio.run(main())
