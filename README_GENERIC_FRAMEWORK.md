# 通用表格数据处理框架

## 概述

本项目实现了一个通用的表格数据处理框架，用于快速添加和处理不同类型的理财产品表格数据。通过配置化的方式，您可以轻松地添加新的 `chart_api` 类型和对应的数据库表，而无需修改核心业务逻辑。

## 🚀 主要特性

- **配置化**: 通过简单配置即可添加新的表格类型
- **通用性**: 统一的数据处理接口，减少重复代码
- **数据验证**: 内置数据验证机制，确保数据质量
- **自动转换**: 自动进行数据类型转换和字段映射
- **冲突处理**: 自动处理数据冲突和去重
- **易扩展**: 支持复杂的数据处理需求
- **详细日志**: 便于问题排查和调试
- **性能优化**: 批量处理和数据库优化

## 📁 项目结构

```
src/nm_crawl/
├── config/
│   └── table_config.py          # 表格配置管理器
├── services/
│   ├── database_service.py      # 数据库服务（已扩展）
│   └── generic_table_service.py # 通用表格服务
├── models/
│   └── financial_models.py      # 数据库模型（已扩展）
└── crawlers/
    └── detail_crawler.py        # 爬虫（已集成框架）

examples/
├── add_new_table_example.py     # 添加新表格的完整示例
└── quick_start_guide.py         # 快速开始指南

docs/
└── GENERIC_TABLE_FRAMEWORK.md   # 详细文档

test_generic_framework.py        # 框架测试脚本
```

## 🎯 现有支持的表格类型

### 1. 历史收益数据 (`get-history-profit`)
- **表名**: `history_profits`
- **字段**: 万份收益、七日年化收益率
- **日期字段**: `profit_date`

### 2. 历史净值数据 (`get-history-net-value`)
- **表名**: `history_netvalue`
- **字段**: 单位净值、累计净值、净值变化、显示条款
- **日期字段**: `net_value_date`

## 🔧 快速开始

### 1. 基本使用

```python
from src.nm_crawl.services.database_service import CmbDatabaseService
from src.nm_crawl.database.connection import init_database

# 初始化数据库
await init_database()

# 创建数据库服务
db_service = CmbDatabaseService()

# 保存数据
profit_data = [
    {"date": "2024-01-01", "tenThousandProfit": "1.23", "sevenDaysAnnualProfit": "4.56"}
]

saved_count = await db_service.save_chart_data(
    "get-history-profit", 
    "PRODUCT001", 
    profit_data, 
    datetime.now()
)
```

### 2. 在爬虫中使用

```python
# 创建爬虫实例，启用数据库保存
crawler = FinancialProductDetailCrawler(save_to_db=True)

# 爬虫会自动识别并处理配置的API数据
# 数据会根据配置自动保存到对应的数据库表
```

## ➕ 如何添加新的表格类型

### 步骤1: 定义数据库表模型

在 `src/nm_crawl/models/financial_models.py` 中添加：

```python
class HistoryPerformance(Base):
    """理财产品历史业绩表"""
    __tablename__ = "history_performance"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(String(100), nullable=False, index=True)
    rip_cod = Column(String(50), nullable=False, index=True)
    
    # 业绩数据字段
    performance_date = Column(String(20), nullable=False)
    annual_return = Column(String(20))
    cumulative_return = Column(String(20))
    # ... 更多字段
```

### 步骤2: 创建表格配置

```python
from src.nm_crawl.config.table_config import TableConfig, FieldMapping, register_table_config

# 定义字段映射
field_mappings = [
    FieldMapping("date", "performance_date", required=True),
    FieldMapping("annualReturn", "annual_return", transformer=convert_to_float),
    # ... 更多映射
]

# 创建配置
performance_config = TableConfig(
    api_name="get-history-performance",
    table_name="history_performance", 
    model_class=HistoryPerformance,
    date_field="performance_date",
    field_mappings=field_mappings
)

# 注册配置
register_table_config(performance_config)
```

### 步骤3: 在爬虫中添加API

在 `detail_crawler.py` 中：

```python
self.chart_apis = [
    "get-history-profit",      # 万份收益
    "get-history-net-value",   # 单位净值  
    "get-history-performance", # 历史业绩 (新增)
]
```

### 步骤4: 运行爬虫

爬虫会自动识别新的API并处理数据！

## 🧪 测试框架

运行完整的测试套件：

```bash
python test_generic_framework.py
```

运行快速开始指南：

```bash
python examples/quick_start_guide.py
```

查看添加新表格的完整示例：

```bash
python examples/add_new_table_example.py
```

## 📊 测试结果

最新测试结果显示所有功能正常：

- ✅ 配置测试: PASSED
- ✅ 历史收益数据处理: PASSED  
- ✅ 历史净值数据处理: PASSED
- ✅ API支持检查: PASSED
- ✅ 数据验证: PASSED

## 🔍 核心组件详解

### 1. TableConfig 配置类

定义表格的完整配置信息：
- API名称和表结构映射
- 字段映射规则
- 数据转换函数
- 数据验证规则

### 2. GenericTableService 通用服务

提供统一的数据处理接口：
- 根据配置动态处理数据
- 自动字段映射和类型转换
- 统一的验证和保存逻辑

### 3. FieldMapping 字段映射

灵活的字段映射配置：
- 源字段到目标字段的映射
- 数据类型转换
- 默认值和验证规则

## 🛠️ 内置功能

### 数据转换函数
- `convert_to_boolean()`: 转换为布尔值
- `convert_to_float()`: 转换为浮点数
- `convert_to_int()`: 转换为整数

### 数据验证函数
- `validate_date_format()`: 验证日期格式
- `validate_profit_data()`: 验证收益数据
- `validate_netvalue_data()`: 验证净值数据

## 📈 性能优化

- 批量数据处理
- 数据库连接池
- 自动冲突处理和去重
- 索引优化

## 🐛 故障排除

### 常见问题

1. **配置未生效**: 确保配置已注册到 `table_config_manager`
2. **字段映射错误**: 检查源数据字段名与配置匹配
3. **数据类型转换失败**: 检查转换函数的异常处理
4. **数据库约束冲突**: 确保唯一索引字段组合正确

### 调试技巧

- 启用详细日志记录
- 使用示例数据测试
- 检查数据库表结构
- 验证API名称配置

## 🔮 扩展性

框架具有良好的扩展性：

- 支持自定义数据提取逻辑
- 支持复杂的数据转换规则  
- 支持多种数据验证策略
- 支持动态配置加载

## 📝 最佳实践

1. **字段命名**: 使用清晰、一致的命名规范
2. **数据验证**: 为每个表格定义适当的验证规则
3. **错误处理**: 在转换函数中添加错误处理
4. **索引优化**: 为常查询字段添加索引
5. **文档更新**: 添加新表格时更新文档

## 🎉 总结

通过这个通用表格数据处理框架，您可以：

- **快速添加**新的表格类型，无需修改核心代码
- **统一处理**不同类型的数据，减少重复开发
- **确保质量**通过内置验证机制保证数据质量
- **提高效率**配置化的开发方式大大提高开发效率

框架已经过完整测试，可以投入生产使用。开始享受配置化的数据处理体验吧！
