#!/usr/bin/env python3

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def main():
    try:
        print("开始直接测试数据库操作...", flush=True)
        
        # 导入必要的模块
        from src.nm_crawl.database.connection import init_database
        from src.nm_crawl.services.database_service import CmbDatabaseService
        
        print("模块导入成功", flush=True)
        
        # 初始化数据库
        await init_database()
        print("数据库初始化成功", flush=True)
        
        # 创建服务
        service = CmbDatabaseService()
        print("服务创建成功", flush=True)
        
        # 获取统计
        product_count = await service.get_product_count()
        history_count = await service.get_history_count()
        
        print(f"产品数量: {product_count}", flush=True)
        print(f"历史记录数量: {history_count}", flush=True)
        
        print("✅ 测试完成！", flush=True)
        
    except Exception as e:
        print(f"❌ 错误: {e}", flush=True)
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
