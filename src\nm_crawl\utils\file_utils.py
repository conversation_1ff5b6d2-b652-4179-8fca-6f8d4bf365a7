# 在 utils 目录下创建 file_utils.py 文件
"""
File utilities for reading and writing files
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, List, Union, Optional
from loguru import logger


def write_json_file(data: Union[Dict[str, Any], List[Any]], file_path: str, 
                   ensure_ascii: bool = False, indent: Optional[int] = 2,
                   encoding: str = 'utf-8') -> bool:
    """
    将数据写入JSON文件
    
    Args:
        data: 要写入的数据（字典或列表）
        file_path: 文件路径
        ensure_ascii: 是否确保ASCII编码
        indent: JSON缩进空格数，None表示不格式化
        encoding: 文件编码，默认utf-8
        
    Returns:
        bool: 是否写入成功
    """
    try:
        # 确保目录存在
        path = Path(file_path)
        path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入JSON文件
        with open(file_path, 'w', encoding=encoding) as f:
            json.dump(data, f, ensure_ascii=ensure_ascii, indent=indent, default=str)
        
        logger.debug(f"Successfully wrote data to {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to write JSON file {file_path}: {e}")
        return False


def read_json_file(file_path: str, encoding: str = 'utf-8') -> Optional[Union[Dict[str, Any], List[Any]]]:
    """
    从JSON文件读取数据
    
    Args:
        file_path: 文件路径
        encoding: 文件编码，默认utf-8
        
    Returns:
        Optional[Union[Dict[str, Any], List[Any]]]: 读取的数据，失败时返回None
    """
    try:
        if not os.path.exists(file_path):
            logger.warning(f"JSON file not found: {file_path}")
            return None
            
        with open(file_path, 'r', encoding=encoding) as f:
            data = json.load(f)
        
        logger.debug(f"Successfully read data from {file_path}")
        return data
        
    except Exception as e:
        logger.error(f"Failed to read JSON file {file_path}: {e}")
        return None


def write_text_file(content: str, file_path: str, encoding: str = 'utf-8') -> bool:
    """
    将文本内容写入文件
    
    Args:
        content: 文本内容
        file_path: 文件路径
        encoding: 文件编码，默认utf-8
        
    Returns:
        bool: 是否写入成功
    """
    try:
        # 确保目录存在
        path = Path(file_path)
        path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入文本文件
        with open(file_path, 'w', encoding=encoding) as f:
            f.write(content)
        
        logger.debug(f"Successfully wrote text to {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to write text file {file_path}: {e}")
        return False


def append_text_file(content: str, file_path: str, encoding: str = 'utf-8') -> bool:
    """
    将文本内容追加到文件
    
    Args:
        content: 文本内容
        file_path: 文件路径
        encoding: 文件编码，默认utf-8
        
    Returns:
        bool: 是否追加成功
    """
    try:
        # 确保目录存在
        path = Path(file_path)
        path.parent.mkdir(parents=True, exist_ok=True)
        
        # 追加文本到文件
        with open(file_path, 'a', encoding=encoding) as f:
            f.write(content)
        
        logger.debug(f"Successfully appended text to {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to append text to file {file_path}: {e}")
        return False


def ensure_directory_exists(directory_path: str) -> bool:
    """
    确保目录存在，如果不存在则创建
    
    Args:
        directory_path: 目录路径
        
    Returns:
        bool: 是否成功确保目录存在
    """
    try:
        Path(directory_path).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"Failed to create directory {directory_path}: {e}")
        return False


def file_exists(file_path: str) -> bool:
    """
    检查文件是否存在
    
    Args:
        file_path: 文件路径
        
    Returns:
        bool: 文件是否存在
    """
    return os.path.exists(file_path)


def get_file_size(file_path: str) -> Optional[int]:
    """
    获取文件大小（字节）
    
    Args:
        file_path: 文件路径
        
    Returns:
        Optional[int]: 文件大小（字节），失败时返回None
    """
    try:
        if file_exists(file_path):
            return os.path.getsize(file_path)
        return None
    except Exception as e:
        logger.error(f"Failed to get file size for {file_path}: {e}")
        return None
