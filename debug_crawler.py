#!/usr/bin/env python3
"""
Debug crawler for troubleshooting
"""

import asyncio
import json
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from crawl4ai import AsyncWebCrawler
from loguru import logger


async def debug_crawl_list():
    """调试版本的列表爬取"""
    
    # 招商银行理财产品列表页URL
    list_page_url = (
        "https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html"
        "?_u=24&PrdTyp=A&behavior_entryid=LC0JGQ001&behavior_pageid=459C25E0"
        "&behavior_slotid=&appflag=1&_urlrefer=PLATFORM&DeviceType=E"
        "&Version=13.3.3&SystemVersion=11"
    )
    
    # 目标API接口
    list_api_url = "https://mobile.cmbchina.com/ientrustfinance/financelist/cashlistnew"
    
    logger.info(f"开始调试爬取: {list_page_url}")
    
    try:
        async with AsyncWebCrawler(verbose=True) as crawler:
            # 配置浏览器选项
            browser_config = {
                "headless": False,  # 显示浏览器窗口以便调试
                "args": [
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-blink-features=AutomationControlled",
                    "--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor"
                ]
            }
            
            # 简化的JavaScript代码
            js_code = f"""
            console.log('页面加载完成，开始监听网络请求');
            
            // 存储捕获的请求
            const capturedRequests = [];
            const targetUrl = '{list_api_url}';
            
            // 监听所有网络请求
            const originalFetch = window.fetch;
            window.fetch = function(url, options = {{}}) {{
                console.log('Fetch请求:', url);
                
                return originalFetch(url, options).then(async response => {{
                    console.log('Fetch响应:', url, response.status);
                    
                    if (url.includes('cashlistnew') || url.includes('financelist')) {{
                        try {{
                            const clonedResponse = response.clone();
                            const text = await clonedResponse.text();
                            console.log('捕获到目标响应:', url);
                            console.log('响应内容长度:', text.length);
                            console.log('响应内容预览:', text.substring(0, 200));
                            
                            capturedRequests.push({{
                                url: url,
                                status: response.status,
                                response: text,
                                timestamp: new Date().toISOString()
                            }});
                        }} catch (e) {{
                            console.error('处理响应失败:', e);
                        }}
                    }}
                    return response;
                }});
            }};
            
            // 监听XMLHttpRequest
            const originalXHR = window.XMLHttpRequest;
            window.XMLHttpRequest = function() {{
                const xhr = new originalXHR();
                const originalOpen = xhr.open;
                const originalSend = xhr.send;
                
                xhr.open = function(method, url, ...args) {{
                    this._method = method;
                    this._url = url;
                    console.log('XHR请求:', method, url);
                    return originalOpen.apply(this, [method, url, ...args]);
                }};
                
                xhr.send = function(data) {{
                    const self = this;
                    this.addEventListener('readystatechange', function() {{
                        if (this.readyState === 4) {{
                            console.log('XHR响应:', self._method, self._url, this.status);
                            
                            if (self._url && (self._url.includes('cashlistnew') || self._url.includes('financelist'))) {{
                                console.log('捕获到目标XHR响应:', self._url);
                                console.log('响应内容长度:', this.responseText.length);
                                console.log('响应内容预览:', this.responseText.substring(0, 200));
                                
                                capturedRequests.push({{
                                    url: self._url,
                                    method: self._method,
                                    status: this.status,
                                    response: this.responseText,
                                    timestamp: new Date().toISOString()
                                }});
                            }}
                        }}
                    }});
                    return originalSend.apply(this, [data]);
                }};
                
                return xhr;
            }};
            
            // 等待页面完全加载
            await new Promise(resolve => setTimeout(resolve, 3000));
            console.log('开始模拟用户操作');
            
            // 尝试滚动页面
            for (let i = 0; i < 3; i++) {{
                console.log(`滚动尝试 ${{i + 1}}`);
                window.scrollTo(0, document.body.scrollHeight);
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 检查是否有新的请求
                console.log(`当前捕获的请求数量: ${{capturedRequests.length}}`);
            }}
            
            // 尝试点击可能的按钮
            const buttons = document.querySelectorAll('button, .btn, [role="button"]');
            console.log(`找到 ${{buttons.length}} 个按钮`);
            
            for (const btn of buttons) {{
                const text = btn.textContent || btn.innerText || '';
                if (text.includes('更多') || text.includes('加载') || text.includes('刷新')) {{
                    try {{
                        console.log('点击按钮:', text);
                        btn.click();
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }} catch (e) {{
                        console.log('点击按钮失败:', e);
                    }}
                }}
            }}
            
            // 最后等待
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            console.log(`最终捕获的请求数量: ${{capturedRequests.length}}`);
            capturedRequests.forEach((req, index) => {{
                console.log(`请求 ${{index + 1}}: ${{req.url}} - ${{req.status}}`);
            }});
            
            // 返回结果
            window.capturedRequests = capturedRequests;
            return capturedRequests;
            """
            
            logger.info("开始执行爬取...")
            
            # 执行爬取
            result = await crawler.arun(
                url=list_page_url,
                wait_for=30,  # 增加等待时间
                browser_config=browser_config,
                js_code=js_code
            )
            
            logger.info("爬取完成，分析结果...")
            
            # 分析结果
            if hasattr(result, 'js_execution_result') and result.js_execution_result:
                captured_requests = result.js_execution_result
                logger.info(f"捕获到 {len(captured_requests)} 个请求")
                
                # 保存调试信息
                debug_data = {
                    "timestamp": datetime.now().isoformat(),
                    "url": list_page_url,
                    "captured_requests": captured_requests,
                    "html_content_length": len(result.html) if result.html else 0,
                    "markdown_content_length": len(result.markdown) if result.markdown else 0
                }
                
                # 保存到文件
                os.makedirs("debug", exist_ok=True)
                debug_file = f"debug/debug_crawl_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(debug_file, 'w', encoding='utf-8') as f:
                    json.dump(debug_data, f, ensure_ascii=False, indent=2)
                
                logger.info(f"调试信息已保存到: {debug_file}")
                
                # 分析捕获的请求
                products = []
                for req in captured_requests:
                    if req.get('response') and req.get('status') == 200:
                        try:
                            data = json.loads(req['response'])
                            if data.get("sysCode") == 200 and "bizResult" in data:
                                biz_result = data["bizResult"]
                                if biz_result.get("code") == 200 and "data" in biz_result:
                                    prd_list = biz_result["data"].get("prdList", [])
                                    if prd_list:
                                        products.extend(prd_list)
                                        logger.info(f"从请求中提取到 {len(prd_list)} 个产品")
                        except Exception as e:
                            logger.error(f"解析响应失败: {e}")
                
                logger.info(f"总共提取到 {len(products)} 个产品")
                
                if products:
                    # 显示前几个产品
                    for i, product in enumerate(products[:3], 1):
                        logger.info(f"产品 {i}: {product.get('ripSnm', '未知')} - {product.get('prdRat', 'N/A')}")
                else:
                    logger.warning("没有提取到任何产品数据")
                    
                    # 输出调试信息
                    logger.info("调试信息:")
                    logger.info(f"- 页面HTML长度: {len(result.html) if result.html else 0}")
                    logger.info(f"- 捕获的请求数量: {len(captured_requests)}")
                    
                    for i, req in enumerate(captured_requests):
                        logger.info(f"- 请求 {i+1}: {req.get('url', 'N/A')} - 状态: {req.get('status', 'N/A')}")
                        if req.get('response'):
                            logger.info(f"  响应长度: {len(req['response'])}")
                            logger.info(f"  响应预览: {req['response'][:200]}...")
                
                return products
            else:
                logger.error("没有获取到JavaScript执行结果")
                return []
                
    except Exception as e:
        logger.error(f"爬取失败: {e}")
        import traceback
        traceback.print_exc()
        return []


async def main():
    """主函数"""
    logger.info("开始调试爬虫")
    
    try:
        products = await debug_crawl_list()
        
        if products:
            logger.info(f"✓ 成功获取到 {len(products)} 个产品")
        else:
            logger.warning("✗ 没有获取到任何产品")
            
            print("\n可能的原因:")
            print("1. 网站结构发生变化")
            print("2. 需要登录或验证")
            print("3. IP被限制")
            print("4. JavaScript执行失败")
            print("5. 网络连接问题")
            
            print("\n建议:")
            print("1. 检查debug/目录下的调试文件")
            print("2. 手动访问目标网站确认是否正常")
            print("3. 尝试使用不同的User-Agent")
            print("4. 检查是否需要设置代理")
        
    except Exception as e:
        logger.error(f"调试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 设置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:HH:mm:ss} | {level} | {message}")
    
    # 运行调试
    asyncio.run(main())
