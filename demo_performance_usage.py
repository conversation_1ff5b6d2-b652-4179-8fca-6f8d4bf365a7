"""
演示如何使用 get-history-performance API
处理您提供的业绩数据
"""

# 模拟您提供的API响应数据
api_response = {
    "sysCode": 200,
    "sysMsg": "Success",
    "bizResult": {
        "code": 200,
        "data": {
            "list": [
                {
                    "prfTyp": "A",
                    "timeInterval": "近1月",
                    "netValueChange": "0.23",
                    "yeaYld": "2.68"
                },
                {
                    "prfTyp": "B",
                    "timeInterval": "近3月",
                    "netValueChange": "0.75",
                    "yeaYld": "2.96"
                },
                {
                    "prfTyp": "J",
                    "timeInterval": "今年以来",
                    "netValueChange": "1.36",
                    "yeaYld": "3.33"
                },
                {
                    "prfTyp": "F",
                    "timeInterval": "成立以来",
                    "netValueChange": "1.36",
                    "yeaYld": "3.33"
                }
            ],
            "yearList": [],
            "yldSwh": "Y",
            "zdfSwh": "Y",
            "nvcShw": "Y"
        }
    }
}

def extract_performance_data(api_response):
    """从API响应中提取业绩数据"""
    try:
        # 提取业绩数据列表
        performance_list = api_response["bizResult"]["data"]["list"]
        
        print(f"提取到 {len(performance_list)} 条业绩数据:")
        
        for i, item in enumerate(performance_list, 1):
            print(f"  数据 {i}:")
            print(f"    业绩类型: {item['prfTyp']}")
            print(f"    时间区间: {item['timeInterval']}")
            print(f"    净值变化: {item['netValueChange']}")
            print(f"    年化收益: {item['yeaYld']}")
            print()
        
        return performance_list
        
    except Exception as e:
        print(f"数据提取失败: {e}")
        return []

def show_field_mapping():
    """显示字段映射关系"""
    print("字段映射关系:")
    print("  API字段 -> 数据库字段")
    print("  prfTyp -> prf_typ (业绩类型)")
    print("  timeInterval -> time_interval (时间区间)")
    print("  netValueChange -> net_value_change (净值变化)")
    print("  yeaYld -> yea_yld (年化收益)")
    print()

def show_database_structure():
    """显示数据库表结构"""
    print("数据库表结构 (history_performance):")
    print("  - id: 自增主键")
    print("  - product_id: 产品ID (cmb_产品代码)")
    print("  - rip_cod: 产品代码")
    print("  - prf_typ: 业绩类型 (A/B/J/F)")
    print("  - time_interval: 时间区间")
    print("  - net_value_change: 净值变化")
    print("  - yea_yld: 年化收益率")
    print("  - raw_data: 完整原始数据 (JSON)")
    print("  - created_at: 创建时间")
    print("  - crawl_time: 爬取时间")
    print()
    
    print("唯一约束:")
    print("  - (product_id, prf_typ, time_interval) 组合唯一")
    print("  - 支持数据更新和去重")
    print()

def show_usage_example():
    """显示使用示例"""
    print("使用示例:")
    print("""
# 1. 在爬虫中添加API支持
self.chart_apis = [
    "get-history-profit",      # 万份收益
    "get-history-net-value",   # 单位净值
    "get-history-performance", # 历史业绩 (新增)
]

# 2. 爬虫会自动识别并处理数据
# 当检测到 get-history-performance API 时，会自动:
# - 提取 bizResult.data.list 中的数据
# - 根据字段映射转换数据
# - 验证数据完整性
# - 保存到 history_performance 表
# - 处理数据冲突和去重

# 3. 手动保存数据 (如果需要)
from src.nm_crawl.services.database_service import CmbDatabaseService

db_service = CmbDatabaseService()
saved_count = await db_service.save_chart_data(
    "get-history-performance", 
    "产品代码", 
    performance_data, 
    datetime.now()
)
""")

def main():
    """主函数"""
    print("=" * 60)
    print("get-history-performance API 使用演示")
    print("=" * 60)
    
    # 显示数据库结构
    show_database_structure()
    
    # 显示字段映射
    show_field_mapping()
    
    # 提取并显示数据
    performance_data = extract_performance_data(api_response)
    
    # 显示使用示例
    show_usage_example()
    
    print("=" * 60)
    print("总结:")
    print("✓ 已添加 get-history-performance API 支持")
    print("✓ 支持您提供的数据结构")
    print("✓ 自动字段映射和数据验证")
    print("✓ 支持数据去重和更新")
    print("✓ 爬虫会自动处理此类数据")
    print("=" * 60)

if __name__ == "__main__":
    main()
