"""
重新创建数据库表
确保所有表结构都是最新的
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.database.connection import init_database, db_manager
from src.nm_crawl.models.financial_models import Base, HistoryPerformance
from sqlalchemy import text


async def recreate_database():
    """重新创建数据库表"""

    print("=== 重新创建数据库表 ===")

    try:
        # 初始化数据库管理器
        manager = await init_database()

        # 删除现有的 history_performance 表（如果存在）
        async with manager.engine.begin() as conn:
            print("1. 删除现有的 history_performance 表...")
            await conn.execute(text("DROP TABLE IF EXISTS history_performance"))
            print("   ✓ 表已删除")

        # 重新创建所有表
        print("2. 重新创建所有表...")
        await manager.create_tables()
        print("   ✓ 所有表已创建")

        # 验证表结构
        async with manager.engine.begin() as conn:
            print("3. 验证 history_performance 表结构...")
            result = await conn.execute(text("PRAGMA table_info(history_performance)"))
            columns = result.fetchall()

            print("   表字段:")
            for col in columns:
                print(f"     - {col[1]} ({col[2]})")

            # 检查必要的字段是否存在
            column_names = [col[1] for col in columns]
            required_fields = ['prf_typ', 'time_interval', 'net_value_change', 'yea_yld']

            missing_fields = [field for field in required_fields if field not in column_names]
            if missing_fields:
                print(f"   ✗ 缺少字段: {missing_fields}")
                return False
            else:
                print("   ✓ 所有必要字段都存在")

        print("\n=== 数据库重建完成 ===")
        return True

    except Exception as e:
        print(f"✗ 数据库重建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_table_creation():
    """测试表创建"""
    
    print("\n=== 测试表创建 ===")
    
    try:
        from src.nm_crawl.services.database_service import CmbDatabaseService
        
        # 创建数据库服务
        db_service = CmbDatabaseService()
        
        # 测试数据
        test_data = [
            {
                "prfTyp": "A",
                "timeInterval": "近1月",
                "netValueChange": "0.23",
                "yeaYld": "2.68"
            }
        ]
        
        # 尝试保存数据
        print("测试保存业绩数据...")
        saved_count = await db_service.save_chart_data(
            "get-history-performance", 
            "TEST001", 
            test_data, 
            None
        )
        
        if saved_count > 0:
            print(f"✓ 成功保存 {saved_count} 条测试记录")
            return True
        else:
            print("✗ 没有保存任何记录")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    
    print("开始重新创建数据库...")
    
    # 重新创建数据库
    recreate_success = await recreate_database()
    
    if recreate_success:
        # 测试表创建
        test_success = await test_table_creation()
        
        if test_success:
            print("\n🎉 数据库重建和测试都成功！")
            print("现在可以正常使用 get-history-performance 功能了。")
        else:
            print("\n❌ 数据库重建成功，但测试失败。")
    else:
        print("\n❌ 数据库重建失败。")


if __name__ == "__main__":
    asyncio.run(main())
