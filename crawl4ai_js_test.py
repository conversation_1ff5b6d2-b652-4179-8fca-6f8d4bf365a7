#!/usr/bin/env python3
"""
Test crawl4ai JavaScript execution
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from crawl4ai import AsyncWebCrawler


async def test_js_with_different_methods():
    """测试不同的JavaScript执行方法"""
    
    print("测试不同的JavaScript执行方法...")
    
    try:
        async with AsyncWebCrawler(verbose=True) as crawler:
            test_url = "https://httpbin.org/json"
            
            # 方法1: 使用js_code参数
            print("\n方法1: 使用js_code参数")
            try:
                result1 = await crawler.arun(
                    url=test_url,
                    js_code="return document.title || 'No title';",
                    wait_for=3
                )
                
                print(f"结果1: {getattr(result1, 'js_execution_result', 'No result')}")
            except Exception as e:
                print(f"方法1失败: {e}")
            
            # 方法2: 使用js_only参数
            print("\n方法2: 使用js_only参数")
            try:
                result2 = await crawler.arun(
                    url=test_url,
                    js_only=True,
                    js_code="return 'JavaScript executed successfully';",
                    wait_for=3
                )
                
                print(f"结果2: {getattr(result2, 'js_execution_result', 'No result')}")
            except Exception as e:
                print(f"方法2失败: {e}")
            
            # 方法3: 检查result对象的所有属性
            print("\n方法3: 检查result对象属性")
            try:
                result3 = await crawler.arun(
                    url=test_url,
                    js_code="console.log('Test'); return 'test result';",
                    wait_for=3
                )
                
                print("Result对象属性:")
                for attr in dir(result3):
                    if not attr.startswith('_'):
                        value = getattr(result3, attr)
                        if not callable(value):
                            print(f"  {attr}: {type(value)} = {str(value)[:100]}")
                
            except Exception as e:
                print(f"方法3失败: {e}")
            
            return True
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_bank_page_basic():
    """测试银行页面基本访问"""
    
    print("\n测试银行页面基本访问...")
    
    list_page_url = (
        "https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html"
        "?_u=24&PrdTyp=A&behavior_entryid=LC0JGQ001&behavior_pageid=459C25E0"
        "&behavior_slotid=&appflag=1&_urlrefer=PLATFORM&DeviceType=E"
        "&Version=13.3.3&SystemVersion=11"
    )
    
    try:
        async with AsyncWebCrawler(verbose=True) as crawler:
            # 配置移动端浏览器
            browser_config = {
                "headless": False,
                "viewport": {"width": 414, "height": 896},
                "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1"
            }
            
            print(f"访问银行页面: {list_page_url}")
            
            result = await crawler.arun(
                url=list_page_url,
                browser_config=browser_config,
                wait_for=10
            )
            
            print(f"页面访问成功")
            print(f"HTML长度: {len(result.html) if result.html else 0}")
            
            # 检查页面内容
            if result.html:
                if "理财" in result.html:
                    print("✓ 页面包含理财相关内容")
                else:
                    print("✗ 页面不包含理财相关内容")
                
                if "骨架屏" in result.html or "skeleton" in result.html.lower():
                    print("⚠ 页面显示骨架屏，数据可能还在加载")
                
                # 保存页面内容
                with open("debug/bank_page.html", "w", encoding="utf-8") as f:
                    f.write(result.html)
                print("页面内容已保存到 debug/bank_page.html")
            
            return True
            
    except Exception as e:
        print(f"银行页面测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("crawl4ai JavaScript和网络监听测试")
    print("=" * 60)
    
    # 确保debug目录存在
    import os
    os.makedirs("debug", exist_ok=True)
    
    # 测试1: JavaScript执行
    success1 = await test_js_with_different_methods()
    
    # 测试2: 银行页面访问
    success2 = await test_bank_page_basic()
    
    print("\n" + "=" * 60)
    print("测试结果:")
    print(f"JavaScript执行: {'✓ 成功' if success1 else '✗ 失败'}")
    print(f"银行页面访问: {'✓ 成功' if success2 else '✗ 失败'}")
    
    if success2:
        print("\n建议:")
        print("1. 检查 debug/bank_page.html 文件内容")
        print("2. 如果页面显示骨架屏，需要等待更长时间")
        print("3. 可能需要模拟更复杂的用户行为")


if __name__ == "__main__":
    asyncio.run(main())
