"""
Tests for data processing utilities
"""

import pytest
from src.nm_crawl.utils.data_processor import DataProcessor


class TestDataProcessor:
    """测试数据处理器"""
    
    def setup_method(self):
        """设置测试方法"""
        self.processor = DataProcessor()
    
    def test_clean_string(self):
        """测试字符串清理"""
        # 正常字符串
        assert self.processor.clean_string("test string") == "test string"
        
        # 包含HTML标签的字符串
        assert self.processor.clean_string("<span>test</span>") == "test"
        assert self.processor.clean_string("<div class='test'>content</div>") == "content"
        
        # 包含多余空白的字符串
        assert self.processor.clean_string("  test   string  ") == "test string"
        assert self.processor.clean_string("test\n\nstring") == "test string"
        
        # 空字符串和None
        assert self.processor.clean_string("") is None
        assert self.processor.clean_string(None) is None
        assert self.processor.clean_string("   ") is None
        
        # 非字符串类型
        assert self.processor.clean_string(123) == "123"
        assert self.processor.clean_string(True) == "True"
    
    def test_clean_rate(self):
        """测试收益率清理"""
        # 正常收益率
        assert self.processor.clean_rate("2.99%") == "2.99%"
        assert self.processor.clean_rate("2.99") == "2.99%"
        
        # 包含HTML标签的收益率
        assert self.processor.clean_rate("2.99<span>%</span>") == "2.99%"
        assert self.processor.clean_rate("<span>2.99</span>%") == "2.99%"
        
        # 复杂格式
        assert self.processor.clean_rate("1.80<span class=\"ef-rat18\">%</span>") == "1.80%"
        
        # 无效输入
        assert self.processor.clean_rate("") is None
        assert self.processor.clean_rate(None) is None
        assert self.processor.clean_rate("无收益") == "无收益"
    
    def test_clean_amount(self):
        """测试金额清理"""
        # 正常金额
        assert self.processor.clean_amount("0.01元起购") == "0.01元起购"
        assert self.processor.clean_amount("1000万元") == "1000万元"
        
        # 包含HTML标签
        assert self.processor.clean_amount("<span>0.01元</span>起购") == "0.01元起购"
        
        # 空值
        assert self.processor.clean_amount("") is None
        assert self.processor.clean_amount(None) is None
    
    def test_parse_date(self):
        """测试日期解析"""
        # 标准格式
        assert self.processor.parse_date("2025-08-21") == "2025-08-21"
        assert self.processor.parse_date("2025-8-1") == "2025-08-01"
        
        # 中文格式
        assert self.processor.parse_date("2025年08月21日") == "2025-08-21"
        assert self.processor.parse_date("2025年8月1日") == "2025-08-01"
        
        # 简短格式（假设当年）
        from datetime import datetime
        current_year = datetime.now().year
        assert self.processor.parse_date("08.21") == f"{current_year}-08-21"
        assert self.processor.parse_date("8.1") == f"{current_year}-08-01"
        
        # 无效输入
        assert self.processor.parse_date("") is None
        assert self.processor.parse_date(None) is None
        assert self.processor.parse_date("invalid date") == "invalid date"
    
    def test_process_product_list_item(self):
        """测试产品列表项处理"""
        raw_product = {
            "ripCod": "GY030111",
            "saaCod": "D07",
            "ripSnm": "工银添利宝现金28",
            "prdRat": "2.99%",
            "ratDes": "7日年化",
            "prdInf": "买入下个工作日享收益",
            "terDay": "赎回下个工作日到账",
            "zylTag": "I 代销工银理财",
            "newFlg": "Y",
            "sellOut": "N",
            "prfOpenTag": False,
            "prdTags": [
                {
                    "tagType": "200007.YX",
                    "tagName": "近一周超万人浏览"
                }
            ]
        }
        
        processed = self.processor.process_product_list_item(raw_product)
        
        assert processed["rip_cod"] == "GY030111"
        assert processed["saa_cod"] == "D07"
        assert processed["rip_snm"] == "工银添利宝现金28"
        assert processed["prd_rat"] == "2.99%"
        assert processed["prf_open_tag"] == False
        assert len(processed["prd_tags"]) == 1
        assert processed["prd_tags"][0]["tagName"] == "近一周超万人浏览"
    
    def test_process_product_detail(self):
        """测试产品详情处理"""
        raw_detail = {
            "saaCod": "D07",
            "ripInn": "PA030144",
            "ripSnm": "平安理财天天成长3号84期A",
            "crpNam": "平安理财有限责任公司",
            "rateText": "1.80<span class=\"ef-rat18\">%</span>",
            "rateName": "七日年化",
            "riskLvl": "R1低风险",
            "sbsUqt": "0.01元起购",
            "investTyp": "现金理财",
            "runDat": "2025-03-20",
            "features": [
                {
                    "zTsdTyp": "A",
                    "zTsdTtl": "申赎灵活，活钱理财"
                }
            ]
        }
        
        processed = self.processor.process_product_detail(raw_detail)
        
        assert processed["saa_cod"] == "D07"
        assert processed["rip_inn"] == "PA030144"
        assert processed["crp_nam"] == "平安理财有限责任公司"
        assert processed["rate_text"] == "1.80%"
        assert processed["risk_lvl"] == "R1低风险"
        assert processed["run_dat"] == "2025-03-20"
        assert len(processed["features"]) == 1
    
    def test_process_history_profit_item(self):
        """测试历史收益项处理"""
        raw_profit = {
            "date": "2025-08-21",
            "tenThousandProfit": "0.4749",
            "sevenDaysAnnualProfit": "1.80"
        }
        
        processed = self.processor.process_history_profit_item(
            raw_profit, "PA030144", "D07"
        )
        
        assert processed["rip_cod"] == "PA030144"
        assert processed["saa_cod"] == "D07"
        assert processed["date"] == "2025-08-21"
        assert processed["ten_thousand_profit"] == "0.4749"
        assert processed["seven_days_annual_profit"] == "1.80%"
    
    def test_merge_product_data(self):
        """测试产品数据合并"""
        list_data = {
            "ripCod": "GY030111",
            "saaCod": "D07",
            "ripSnm": "工银添利宝现金28",
            "prdRat": "2.99%"
        }
        
        detail_data = {
            "saaCod": "D07",
            "ripInn": "GY030111",
            "crpNam": "工银理财有限责任公司",
            "riskLvl": "R1低风险"
        }
        
        merged = self.processor.merge_product_data(list_data, detail_data)
        
        # 检查列表数据
        assert merged["rip_cod"] == "GY030111"
        assert merged["prd_rat"] == "2.99%"
        
        # 检查详情数据
        assert merged["crp_nam"] == "工银理财有限责任公司"
        assert merged["risk_lvl"] == "R1低风险"
    
    def test_validate_product_data(self):
        """测试产品数据验证"""
        # 有效数据
        valid_product = {
            "rip_cod": "GY030111",
            "saa_cod": "D07",
            "rip_snm": "工银添利宝现金28"
        }
        assert self.processor.validate_product_data(valid_product) == True
        
        # 缺少必需字段
        invalid_product1 = {
            "rip_cod": "GY030111",
            "saa_cod": "D07"
            # 缺少 rip_snm
        }
        assert self.processor.validate_product_data(invalid_product1) == False
        
        # 字段为空
        invalid_product2 = {
            "rip_cod": "",
            "saa_cod": "D07",
            "rip_snm": "工银添利宝现金28"
        }
        assert self.processor.validate_product_data(invalid_product2) == False
    
    def test_validate_history_profit_data(self):
        """测试历史收益数据验证"""
        # 有效数据
        valid_profit = {
            "rip_cod": "GY030111",
            "saa_cod": "D07",
            "date": "2025-08-21"
        }
        assert self.processor.validate_history_profit_data(valid_profit) == True
        
        # 缺少必需字段
        invalid_profit = {
            "rip_cod": "GY030111",
            "saa_cod": "D07"
            # 缺少 date
        }
        assert self.processor.validate_history_profit_data(invalid_profit) == False
