#!/usr/bin/env python3
"""
测试数据库连接和会话管理
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_db_connection():
    """测试数据库连接"""
    try:
        print("🔧 测试数据库连接和会话管理")
        print("=" * 50)
        
        # 导入必要的模块
        from src.nm_crawl.database.connection import init_database, get_db_session, close_database
        from src.nm_crawl.services.database_service import CmbDatabaseService
        
        print("✅ 模块导入成功")
        
        # 初始化数据库
        print("📊 初始化数据库...")
        await init_database()
        print("✅ 数据库初始化成功")
        
        # 测试数据库服务
        print("🔍 测试数据库服务...")
        db_service = CmbDatabaseService()
        
        # 测试获取产品数量
        product_count = await db_service.get_product_count()
        print(f"📦 产品数量: {product_count}")
        
        # 测试获取历史记录数量
        try:
            history_count = await db_service.get_history_count()
            print(f"📊 历史记录数量: {history_count}")
        except Exception as e:
            print(f"⚠️  历史记录查询失败: {e}")
        
        # 关闭数据库连接
        print("🔒 关闭数据库连接...")
        await close_database()
        print("✅ 数据库连接已关闭")
        
        print("\n🎉 数据库连接测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_db_connection())
