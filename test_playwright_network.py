#!/usr/bin/env python3
"""
Test network monitoring using playwright directly
"""

import asyncio
import json
import sys
import os
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from playwright.async_api import async_playwright
from loguru import logger


class PlaywrightNetworkListener:
    """使用Playwright直接监听网络请求"""
    
    def __init__(self):
        self.captured_requests = []
        self.target_apis = ['cashlistnew', 'financelist', 'productlist']
    
    def is_target_request(self, url: str) -> bool:
        """检查是否为目标请求"""
        return any(api in url.lower() for api in self.target_apis)
    
    async def on_request(self, request):
        """请求监听器"""
        if self.is_target_request(request.url):
            logger.info(f"捕获到目标请求: {request.method} {request.url}")
    
    async def on_response(self, response):
        """响应监听器"""
        if self.is_target_request(response.url) and response.status == 200:
            try:
                logger.info(f"捕获到目标响应: {response.status} {response.url}")
                
                # 获取响应内容
                response_text = await response.text()
                
                request_data = {
                    "url": response.url,
                    "method": response.request.method,
                    "status": response.status,
                    "response": response_text,
                    "timestamp": datetime.now().isoformat(),
                    "headers": dict(response.headers)
                }
                
                self.captured_requests.append(request_data)
                logger.info(f"保存响应数据，长度: {len(response_text)}")
                
            except Exception as e:
                logger.error(f"处理响应失败: {e}")
    
    async def crawl_with_network_monitoring(self, wait_time: int = 30) -> List[Dict[str, Any]]:
        """使用网络监听爬取"""
        
        list_page_url = (
            "https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html"
            "?_u=24&PrdTyp=A&behavior_entryid=LC0JGQ001&behavior_pageid=459C25E0"
            "&behavior_slotid=&appflag=1&_urlrefer=PLATFORM&DeviceType=E"
            "&Version=13.3.3&SystemVersion=11"
        )
        
        logger.info(f"开始网络监听爬取: {list_page_url}")
        
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(
                headless=False,  # 显示浏览器
                args=[
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-blink-features=AutomationControlled"
                ]
            )
            
            # 创建上下文
            context = await browser.new_context(
                viewport={"width": 414, "height": 896},
                user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1"
            )
            
            # 创建页面
            page = await context.new_page()
            
            # 设置网络监听器
            page.on("request", self.on_request)
            page.on("response", self.on_response)
            
            try:
                logger.info("导航到目标页面...")
                await page.goto(list_page_url, wait_until="networkidle", timeout=60000)
                
                logger.info("页面加载完成，开始用户行为模拟...")
                
                # 等待页面完全加载
                await page.wait_for_timeout(8000)
                
                # 模拟用户滚动
                for i in range(3):
                    logger.info(f"滚动 {i + 1}")
                    await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                    await page.wait_for_timeout(3000)
                    
                    await page.evaluate("window.scrollTo(0, 0)")
                    await page.wait_for_timeout(1500)
                
                # 尝试点击标签或按钮
                logger.info("尝试点击页面元素...")
                
                # 查找可点击的元素
                clickable_selectors = [
                    '.swiper-slide',
                    '.tab-item', 
                    'button',
                    '[role="tab"]',
                    '.tab',
                    '.btn'
                ]
                
                for selector in clickable_selectors:
                    try:
                        elements = await page.query_selector_all(selector)
                        logger.info(f"找到 {len(elements)} 个 {selector} 元素")
                        
                        for i, element in enumerate(elements[:3]):  # 只点击前3个
                            try:
                                # 检查元素是否可见
                                is_visible = await element.is_visible()
                                if is_visible:
                                    text = await element.inner_text()
                                    logger.info(f"点击元素: {selector}[{i}] - {text[:20]}")
                                    
                                    await element.click()
                                    await page.wait_for_timeout(4000)
                                else:
                                    logger.info(f"元素不可见: {selector}[{i}]")
                            except Exception as e:
                                logger.warning(f"点击元素失败: {selector}[{i}] - {e}")
                    except Exception as e:
                        logger.warning(f"查找元素失败: {selector} - {e}")
                
                # 最后等待
                logger.info("最后等待...")
                await page.wait_for_timeout(5000)
                
                logger.info(f"网络监听完成，捕获到 {len(self.captured_requests)} 个请求")
                
                # 保存调试信息
                await self._save_debug_info(page)
                
                return self.captured_requests
                
            except Exception as e:
                logger.error(f"爬取过程中出错: {e}")
                import traceback
                traceback.print_exc()
                return []
            
            finally:
                await browser.close()
    
    async def _save_debug_info(self, page):
        """保存调试信息"""
        try:
            # 保存页面内容
            html_content = await page.content()
            
            debug_data = {
                "timestamp": datetime.now().isoformat(),
                "captured_requests_count": len(self.captured_requests),
                "captured_requests": self.captured_requests,
                "html_length": len(html_content)
            }
            
            # 保存调试文件
            debug_file = f"debug/playwright_network_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(debug_file, 'w', encoding='utf-8') as f:
                json.dump(debug_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"调试信息已保存到: {debug_file}")
            
            # 保存HTML内容
            html_file = f"debug/playwright_page_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"页面内容已保存到: {html_file}")
            
        except Exception as e:
            logger.error(f"保存调试信息失败: {e}")
    
    def extract_products_from_requests(self) -> List[Dict[str, Any]]:
        """从捕获的请求中提取产品数据"""
        products = []
        
        for request in self.captured_requests:
            try:
                response_text = request.get('response', '')
                if response_text:
                    data = json.loads(response_text)
                    
                    # 处理招商银行API响应格式
                    if data.get("sysCode") in [200, 1014] and "bizResult" in data:
                        biz_result = data["bizResult"]
                        if biz_result.get("code") == 200 and "data" in biz_result:
                            prd_list = biz_result["data"].get("prdList", [])
                            if prd_list:
                                products.extend(prd_list)
                                logger.info(f"从请求中提取到 {len(prd_list)} 个产品")
                
            except json.JSONDecodeError as e:
                logger.warning(f"解析JSON失败: {e}")
            except Exception as e:
                logger.error(f"提取产品数据失败: {e}")
        
        return products


async def main():
    """主函数"""
    logger.info("开始Playwright网络监听测试")
    
    # 确保debug目录存在
    os.makedirs("debug", exist_ok=True)
    
    try:
        # 创建网络监听器
        listener = PlaywrightNetworkListener()
        
        # 执行网络监听爬取
        captured_requests = await listener.crawl_with_network_monitoring(wait_time=40)
        
        if captured_requests:
            logger.info(f"✓ 成功捕获 {len(captured_requests)} 个网络请求")
            
            # 提取产品数据
            products = listener.extract_products_from_requests()
            
            if products:
                logger.info(f"✓ 成功提取 {len(products)} 个产品")
                
                # 显示前几个产品
                for i, product in enumerate(products[:3], 1):
                    name = product.get('ripSnm', '未知产品')
                    rate = product.get('prdRat', 'N/A')
                    code = product.get('ripCod', 'N/A')
                    logger.info(f"  {i}. {name} - {rate} (代码: {code})")
                
                # 保存产品数据
                products_file = f"debug/products_playwright_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(products_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        "products": products,
                        "total_count": len(products),
                        "crawl_time": datetime.now().isoformat(),
                        "source": "playwright_network_listener"
                    }, f, ensure_ascii=False, indent=2)
                
                logger.info(f"产品数据已保存到: {products_file}")
                
                print("\n🎉 Playwright网络监听测试成功！")
                print("现在可以将这个方法集成到主项目中")
                
            else:
                logger.warning("没有提取到任何产品数据")
                print("\n⚠ 捕获到网络请求但没有产品数据")
                print("请检查debug目录下的文件分析原因")
        else:
            logger.error("没有捕获到任何网络请求")
            print("\n❌ 没有捕获到任何网络请求")
            print("可能的原因:")
            print("1. 网站结构发生变化")
            print("2. 需要更长的等待时间")
            print("3. 需要更复杂的用户行为模拟")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 设置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:HH:mm:ss} | {level} | {message}")
    
    # 运行测试
    asyncio.run(main())
