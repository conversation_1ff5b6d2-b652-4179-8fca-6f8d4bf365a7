#!/usr/bin/env python3
"""
查询数据库中的理财产品数据
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy import select, func
from src.nm_crawl.database.connection import init_database, get_db_session
from src.nm_crawl.models.financial_models import ProductList, ProductDetail, HistoryProfits
from loguru import logger


async def query_product_list():
    """查询产品列表"""
    print("\n📋 产品列表数据:")
    print("-" * 60)
    
    async for session in get_db_session():
        try:
            # 查询所有产品
            result = await session.execute(
                select(ProductList).order_by(ProductList.rip_snm)
            )
            products = result.scalars().all()
            
            if products:
                print(f"共找到 {len(products)} 个产品:")
                for i, product in enumerate(products, 1):
                    print(f"{i:2d}. {product.rip_snm}")
                    print(f"     代码: {product.rip_cod}")
                    print(f"     收益率: {product.prd_rat}")
                    print(f"     期限: {product.ter_day}")
                    print(f"     风险等级: {product.zyl_tag}")
                    print()
            else:
                print("没有找到产品数据")
                
        except Exception as e:
            logger.error(f"查询产品列表失败: {e}")


async def query_product_details():
    """查询产品详情"""
    print("\n📄 产品详情数据:")
    print("-" * 60)
    
    async for session in get_db_session():
        try:
            # 查询所有详情
            result = await session.execute(
                select(ProductDetail).order_by(ProductDetail.rip_snm)
            )
            details = result.scalars().all()
            
            if details:
                print(f"共找到 {len(details)} 个产品详情:")
                for i, detail in enumerate(details, 1):
                    print(f"{i}. {detail.rip_snm} ({detail.rip_cod})")
                    print(f"   发行机构: {detail.crp_nam}")
                    print(f"   收益率: {detail.rate_text}")
                    print(f"   风险等级: {detail.risk_lvl}")
                    print(f"   起购金额: {detail.sbs_uqt}")
                    print(f"   投资期限: {detail.ter_day}")
                    print()
            else:
                print("没有找到产品详情数据")
                
        except Exception as e:
            logger.error(f"查询产品详情失败: {e}")


async def query_product_history():
    """查询历史收益"""
    print("\n📊 历史收益数据:")
    print("-" * 60)
    
    async for session in get_db_session():
        try:
            # 查询历史记录数量
            count_result = await session.execute(
                select(func.count(HistoryProfits.id))
            )
            total_count = count_result.scalar()

            if total_count > 0:
                # 查询最近的历史记录
                result = await session.execute(
                    select(HistoryProfits)
                    .order_by(HistoryProfits.profit_date.desc())
                    .limit(10)
                )
                history_records = result.scalars().all()
                
                print(f"共有 {total_count} 条历史记录，显示最近10条:")
                for i, record in enumerate(history_records, 1):
                    print(f"{i:2d}. {record.rip_cod} - {record.profit_date}")
                    print(f"     万份收益: {record.ten_thousand_profit}")
                    print(f"     七日年化: {record.seven_days_annual_profit}")
                    print()
            else:
                print("没有找到历史收益数据")
                
        except Exception as e:
            logger.error(f"查询历史收益失败: {e}")


async def query_statistics():
    """查询统计信息"""
    print("\n📈 数据库统计:")
    print("-" * 60)
    
    async for session in get_db_session():
        try:
            # 产品列表统计
            list_count = await session.execute(select(func.count(ProductList.product_id)))
            list_total = list_count.scalar()

            # 产品详情统计
            detail_count = await session.execute(select(func.count(ProductDetail.product_id)))
            detail_total = detail_count.scalar()

            # 历史记录统计
            history_count = await session.execute(select(func.count(HistoryProfits.id)))
            history_total = history_count.scalar()
            
            print(f"📦 产品列表: {list_total} 个")
            print(f"📄 产品详情: {detail_total} 个")
            print(f"📊 历史记录: {history_total} 条")
            
            # 按风险等级统计
            if list_total > 0:
                risk_stats = await session.execute(
                    select(ProductList.zyl_tag, func.count(ProductList.product_id))
                    .group_by(ProductList.zyl_tag)
                    .order_by(func.count(ProductList.product_id).desc())
                )

                print(f"\n🏷️  按风险等级分布:")
                for risk_level, count in risk_stats:
                    if risk_level:
                        print(f"   {risk_level}: {count} 个")

            # 按收益率统计
            if list_total > 0:
                rate_stats = await session.execute(
                    select(ProductList.prd_rat, func.count(ProductList.product_id))
                    .group_by(ProductList.prd_rat)
                    .order_by(ProductList.prd_rat.desc())
                )

                print(f"\n💰 按收益率分布:")
                for rate, count in rate_stats:
                    if rate:
                        print(f"   {rate}: {count} 个")
                
        except Exception as e:
            logger.error(f"查询统计信息失败: {e}")


async def main():
    """主函数"""
    print("🏦 招商银行理财产品数据库查询")
    print("=" * 60)
    
    try:
        # 初始化数据库
        await init_database()
        
        # 查询各种数据
        await query_statistics()
        await query_product_list()
        await query_product_details()
        await query_product_history()
        
        print("\n✅ 数据库查询完成！")
        print("\n💡 提示:")
        print("- 数据库文件位置: data/financial_products.db")
        print("- 可以使用SQLite工具进一步查询和分析")
        print("- 表名: product_list, product_details, history_profits")
        
    except Exception as e:
        logger.error(f"数据库查询失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 设置简单日志
    logger.remove()
    logger.add(sys.stdout, level="ERROR", format="{time:HH:mm:ss} | {level} | {message}")
    
    # 运行查询
    asyncio.run(main())
