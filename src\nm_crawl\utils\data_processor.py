"""
Data processing and cleaning utilities
"""

import json
import re
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, date
from loguru import logger


class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        """初始化数据处理器"""
        logger.info("DataProcessor initialized")
    
    def clean_string(self, value: Any) -> Optional[str]:
        """
        清理字符串数据
        
        Args:
            value: 输入值
            
        Returns:
            Optional[str]: 清理后的字符串
        """
        if value is None:
            return None
        
        if not isinstance(value, str):
            value = str(value)
        
        # 去除HTML标签
        value = re.sub(r'<[^>]+>', '', value)
        
        # 去除多余的空白字符
        value = re.sub(r'\s+', ' ', value).strip()
        
        # 如果字符串为空，返回None
        return value if value else None
    
    def clean_rate(self, rate_str: Any) -> Optional[str]:
        """
        清理收益率字符串
        
        Args:
            rate_str: 收益率字符串
            
        Returns:
            Optional[str]: 清理后的收益率
        """
        if not rate_str:
            return None
        
        rate_str = str(rate_str)
        
        # 去除HTML标签
        rate_str = re.sub(r'<[^>]+>', '', rate_str)
        
        # 提取数字和百分号
        match = re.search(r'(\d+\.?\d*)%?', rate_str)
        if match:
            return f"{match.group(1)}"
        
        return self.clean_string(rate_str)
    
    def clean_amount(self, amount_str: Any) -> Optional[str]:
        """
        清理金额字符串
        
        Args:
            amount_str: 金额字符串
            
        Returns:
            Optional[str]: 清理后的金额
        """
        if not amount_str:
            return None
        
        amount_str = str(amount_str)
        
        # 去除HTML标签
        amount_str = re.sub(r'<[^>]+>', '', amount_str)
        
        # 去除多余的空白字符
        amount_str = re.sub(r'\s+', ' ', amount_str).strip()
        
        return amount_str if amount_str else None
    
    def parse_date(self, date_str: Any) -> Optional[str]:
        """
        解析日期字符串
        
        Args:
            date_str: 日期字符串
            
        Returns:
            Optional[str]: 标准化的日期字符串 (YYYY-MM-DD)
        """
        if not date_str:
            return None
        
        date_str = str(date_str).strip()
        
        # 尝试不同的日期格式
        date_patterns = [
            r'(\d{4})-(\d{1,2})-(\d{1,2})',  # YYYY-MM-DD
            r'(\d{4})年(\d{1,2})月(\d{1,2})日',  # YYYY年MM月DD日
            r'(\d{1,2})\.(\d{1,2})',  # MM.DD (当年)
            r'(\d{2})\.(\d{1,2})',  # YY.MM (当年)
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, date_str)
            if match:
                groups = match.groups()
                
                if len(groups) == 3:
                    year, month, day = groups
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                elif len(groups) == 2:
                    # 假设是当年的日期
                    current_year = datetime.now().year
                    month, day = groups
                    return f"{current_year}-{month.zfill(2)}-{day.zfill(2)}"
        
        return date_str
    
    def process_product_list_item(self, product: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理产品列表项数据
        
        Args:
            product: 原始产品数据
            
        Returns:
            Dict[str, Any]: 处理后的产品数据
        """
        processed = {}
        
        # 基本信息
        processed["rip_cod"] = self.clean_string(product.get("ripCod"))
        processed["saa_cod"] = self.clean_string(product.get("saaCod"))
        processed["rip_snm"] = self.clean_string(product.get("ripSnm"))
        processed["fnd_nbr"] = self.clean_string(product.get("fndNbr"))
        
        # 收益信息
        processed["prd_rat"] = self.clean_rate(product.get("prdRat"))
        processed["rat_des"] = self.clean_string(product.get("ratDes"))
        processed["prd_inf"] = self.clean_string(product.get("prdInf"))
        processed["ter_day"] = self.clean_string(product.get("terDay"))
        
        # 标签信息
        processed["zyl_tag"] = self.clean_string(product.get("zylTag"))
        processed["dxs_tag"] = self.clean_string(product.get("dxsTag"))
        processed["jjb_tag"] = self.clean_string(product.get("jjbTag"))
        processed["new_flg"] = self.clean_string(product.get("newFlg"))
        processed["sell_out"] = self.clean_string(product.get("sellOut"))
        processed["sal_tim"] = self.clean_string(product.get("salTim"))
        
        # 布尔值
        processed["prf_open_tag"] = product.get("prfOpenTag", False)
        
        # 产品标签列表
        prd_tags = product.get("prdTags", [])
        if isinstance(prd_tags, list):
            processed["prd_tags"] = prd_tags
        else:
            processed["prd_tags"] = []
        
        return processed
    
    def process_product_detail(self, detail: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理产品详情数据
        
        Args:
            detail: 原始详情数据
            
        Returns:
            Dict[str, Any]: 处理后的详情数据
        """
        processed = {}
        
        # 基本信息
        processed["saa_cod"] = self.clean_string(detail.get("saaCod"))
        processed["rip_inn"] = self.clean_string(detail.get("ripInn"))
        processed["rip_snm"] = self.clean_string(detail.get("ripSnm"))
        processed["rip_nbr"] = self.clean_string(detail.get("ripNbr"))
        
        # 公司信息
        processed["crp_nam"] = self.clean_string(detail.get("crpNam"))
        processed["crp_dec"] = self.clean_string(detail.get("crpDec"))
        processed["crp_cod"] = self.clean_string(detail.get("crpCod"))
        
        # 收益信息
        processed["rate_text"] = self.clean_rate(detail.get("rateText"))
        processed["rate_text_top"] = self.clean_string(detail.get("rateTextTop"))
        processed["rate_name"] = self.clean_string(detail.get("rateName"))
        processed["rat_dsc"] = self.clean_string(detail.get("ratDsc"))
        
        # 产品信息
        processed["ter_day"] = self.clean_string(detail.get("terDay"))
        processed["ter_day_name"] = self.clean_string(detail.get("terDayName"))
        processed["risk_lvl"] = self.clean_string(detail.get("riskLvl"))
        processed["sbs_uqt"] = self.clean_amount(detail.get("sbsUqt"))
        processed["invest_typ"] = self.clean_string(detail.get("investTyp"))
        
        # 产品特色
        features = detail.get("features", [])
        if isinstance(features, list):
            processed["features"] = features
        else:
            processed["features"] = []
        
        # 交易规则
        processed["buy_time_name"] = self.clean_string(detail.get("buyTimeName"))
        processed["buy_time_rule"] = self.clean_string(detail.get("buyTimeRule"))
        processed["buy_fee_rule"] = self.clean_string(detail.get("buyFeeRule"))
        processed["confirm_time_rule"] = self.clean_string(detail.get("confirmTimeRule"))
        processed["cancel_rule"] = self.clean_string(detail.get("cancelRule"))
        processed["redeem_fee_rule"] = self.clean_string(detail.get("redeemFeeRule"))
        processed["redeem_way_rule"] = self.clean_string(detail.get("redeemWayRule"))
        processed["pay_time_rule"] = self.clean_string(detail.get("payTimeRule"))
        processed["redemption_limit"] = self.clean_string(detail.get("redemptionLimit"))
        
        # 时间线数据
        timeline_data = detail.get("timeLineData", {})
        if isinstance(timeline_data, dict):
            processed["timeline_data"] = timeline_data
        
        # 其他信息
        processed["run_dat"] = self.parse_date(detail.get("runDat"))
        processed["run_dat1"] = self.clean_string(detail.get("runDat1"))
        processed["per_bnk"] = self.clean_string(detail.get("perBnk"))
        processed["per_bnk_open"] = self.clean_string(detail.get("perBnkOpen"))
        processed["buy_sum"] = self.clean_string(detail.get("buySum"))
        processed["ccy_nbr"] = self.clean_string(detail.get("ccyNbr"))
        
        # 收益数据
        processed["prf_1mn"] = self.clean_string(detail.get("prf1Mn"))
        processed["prf_3mn"] = self.clean_string(detail.get("prf3Mn"))
        processed["prf_6mn"] = self.clean_string(detail.get("prf6Mn"))
        processed["prf_1ye"] = self.clean_string(detail.get("prf1Ye"))
        processed["prf_est"] = self.clean_string(detail.get("prfEst"))
        
        # 按钮信息
        processed["btn_tag"] = self.clean_string(detail.get("btnTag"))
        processed["btn_txt"] = self.clean_string(detail.get("btnTxt"))
        
        return processed
    
    def process_history_profit_item(self, profit: Dict[str, Any], rip_cod: str, saa_cod: str) -> Dict[str, Any]:
        """
        处理历史收益项数据
        
        Args:
            profit: 原始收益数据
            rip_cod: 产品代码
            saa_cod: 销售代码
            
        Returns:
            Dict[str, Any]: 处理后的收益数据
        """
        processed = {}
        
        processed["rip_cod"] = rip_cod
        processed["saa_cod"] = saa_cod
        processed["date"] = self.parse_date(profit.get("date"))
        processed["ten_thousand_profit"] = self.clean_string(profit.get("tenThousandProfit"))
        processed["seven_days_annual_profit"] = self.clean_rate(profit.get("sevenDaysAnnualProfit"))
        
        return processed
    
    def merge_product_data(self, list_data: Dict[str, Any], detail_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        合并产品列表和详情数据
        
        Args:
            list_data: 列表数据
            detail_data: 详情数据
            
        Returns:
            Dict[str, Any]: 合并后的数据
        """
        # 先处理列表数据
        merged = self.process_product_list_item(list_data)
        
        # 如果有详情数据，合并进去
        if detail_data:
            processed_detail = self.process_product_detail(detail_data)
            
            # 合并数据，详情数据优先
            for key, value in processed_detail.items():
                if value is not None:
                    merged[key] = value
        
        return merged
    
    def validate_product_data(self, product: Dict[str, Any]) -> bool:
        """
        验证产品数据的完整性
        
        Args:
            product: 产品数据
            
        Returns:
            bool: 是否有效
        """
        required_fields = ["rip_cod", "saa_cod", "rip_snm"]
        
        for field in required_fields:
            if not product.get(field):
                logger.warning(f"Missing required field: {field}")
                return False
        
        return True
    
    def validate_history_profit_data(self, profit: Dict[str, Any]) -> bool:
        """
        验证历史收益数据的完整性
        
        Args:
            profit: 收益数据
            
        Returns:
            bool: 是否有效
        """
        required_fields = ["rip_cod", "saa_cod", "date"]
        
        for field in required_fields:
            if not profit.get(field):
                logger.warning(f"Missing required field in history profit: {field}")
                return False
        
        return True
