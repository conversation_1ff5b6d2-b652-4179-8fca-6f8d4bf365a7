#!/usr/bin/env python3
"""
测试main.py的基本功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.main import CrawlerApp
from src.nm_crawl.config.settings import AppConfig


async def test_app_initialization():
    """测试应用初始化"""
    print("=== 测试应用初始化 ===")
    
    try:
        app = CrawlerApp("config.json")
        await app.initialize()
        
        print("✅ 应用初始化成功")
        
        # 测试获取状态
        status = app.get_status()
        print(f"数据库URL: {status['config']['database_url']}")
        print(f"数据目录: {status['config']['data_directory']}")
        print(f"调度器状态: {'启用' if status['config']['scheduler_enabled'] else '禁用'}")
        
        await app.cleanup()
        print("✅ 应用清理成功")
        
    except Exception as e:
        print(f"❌ 应用初始化失败: {e}")
        import traceback
        traceback.print_exc()


async def test_custom_config():
    """测试自定义配置"""
    print("\n=== 测试自定义配置 ===")
    
    try:
        # 创建自定义配置
        custom_config = AppConfig(
            debug=True,
            crawler={
                "list_wait_time": 10,
                "detail_wait_time": 8,
                "max_detail_products": 3,
                "headless": True
            },
            logging={
                "level": "DEBUG",
                "log_directory": "logs/test"
            },
            storage={
                "data_directory": "data/test"
            }
        )
        
        # 保存自定义配置
        from src.nm_crawl.config.settings import save_config
        config_file = save_config(custom_config, "test_config.json")
        print(f"✅ 保存自定义配置到: {config_file}")
        
        # 使用自定义配置初始化应用
        app = CrawlerApp("test_config.json")
        await app.initialize()
        
        print("✅ 使用自定义配置初始化成功")
        
        # 验证配置
        status = app.get_status()
        assert status['config']['data_directory'] == "data/test"
        assert status['config']['log_level'] == "DEBUG"
        
        print("✅ 配置验证成功")
        
        await app.cleanup()
        
    except Exception as e:
        print(f"❌ 自定义配置测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_database_connection():
    """测试数据库连接"""
    print("\n=== 测试数据库连接 ===")
    
    try:
        from src.nm_crawl.database.connection import init_database, close_database
        
        # 初始化数据库
        await init_database("sqlite+aiosqlite:///data/test_financial_products.db")
        print("✅ 数据库初始化成功")
        
        # 测试数据库服务
        from src.nm_crawl.services.database_service import CmbDatabaseService
        db_service = CmbDatabaseService()
        
        # 获取统计信息
        product_count = await db_service.get_product_count()
        history_count = await db_service.get_history_count()
        
        print(f"✅ 数据库连接成功，产品数: {product_count}, 历史记录数: {history_count}")
        
        await close_database()
        print("✅ 数据库连接关闭成功")
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_data_processor():
    """测试数据处理器"""
    print("\n=== 测试数据处理器 ===")
    
    try:
        from src.nm_crawl.services.data_processor import DataProcessor
        
        processor = DataProcessor("data/test")
        
        # 获取数据库统计信息
        stats = await processor.get_database_stats()
        print(f"✅ 数据处理器初始化成功，统计信息: {stats}")
        
    except Exception as e:
        print(f"❌ 数据处理器测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_logger_setup():
    """测试日志设置"""
    print("\n=== 测试日志设置 ===")
    
    try:
        from src.nm_crawl.utils.logger import setup_logging, get_monitor
        
        # 设置日志
        setup_logging(log_directory="logs/test", level="INFO")
        print("✅ 日志设置成功")
        
        # 获取监控器
        monitor = get_monitor("logs/test")
        
        # 测试监控功能
        monitor.start_crawl_session("test_session", "test")
        monitor.end_crawl_session("test_session", True, {"test": "data"})
        
        # 获取指标
        metrics = monitor.get_metrics()
        print(f"✅ 监控器测试成功，指标: {metrics['total_crawls']} 次爬取")
        
    except Exception as e:
        print(f"❌ 日志设置测试失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主测试函数"""
    print("开始测试main.py功能")
    print("=" * 50)
    
    # 运行各项测试
    await test_app_initialization()
    await test_custom_config()
    await test_database_connection()
    await test_data_processor()
    test_logger_setup()
    
    print("\n" + "=" * 50)
    print("所有测试完成！")
    
    print("\n✅ main.py 已修复并可以正常运行")
    print("\n可用的命令:")
    print("  python -m src.nm_crawl.main --help")
    print("  python -m src.nm_crawl.main init-config")
    print("  python -m src.nm_crawl.main status")
    print("  python -m src.nm_crawl.main crawl-list")
    print("  python -m src.nm_crawl.main crawl-full")


if __name__ == "__main__":
    asyncio.run(main())
