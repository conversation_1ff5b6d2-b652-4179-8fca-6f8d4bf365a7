#!/usr/bin/env python3
"""
Test JavaScript execution in crawl4ai
"""

import asyncio
import json
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from crawl4ai import AsyncWeb<PERSON>rawler
from loguru import logger


async def test_js_execution_methods():
    """测试不同的JavaScript执行方法"""
    
    logger.info("测试JavaScript执行方法")
    
    # 简单的测试页面
    test_url = "https://httpbin.org/json"
    
    try:
        async with AsyncWebCrawler(verbose=True) as crawler:
            
            # 方法1: 基本JavaScript执行
            logger.info("方法1: 基本JavaScript执行")
            try:
                result1 = await crawler.arun(
                    url=test_url,
                    js_code="return 'Hello from JavaScript';",
                    wait_for=3
                )
                
                logger.info(f"方法1结果: {getattr(result1, 'js_execution_result', 'No result')}")
                
                # 检查所有可能的属性
                logger.info("Result对象属性:")
                for attr in dir(result1):
                    if not attr.startswith('_') and not callable(getattr(result1, attr)):
                        value = getattr(result1, attr)
                        logger.info(f"  {attr}: {type(value)} = {str(value)[:100]}")
                
            except Exception as e:
                logger.error(f"方法1失败: {e}")
            
            # 方法2: 使用不同的参数
            logger.info("\n方法2: 使用不同的参数")
            try:
                result2 = await crawler.arun(
                    url=test_url,
                    js_code="""
                    console.log('JavaScript is running');
                    const result = {
                        title: document.title,
                        url: window.location.href,
                        timestamp: new Date().toISOString()
                    };
                    console.log('Result:', result);
                    return result;
                    """,
                    wait_for=5,
                    js_only=True
                )
                
                logger.info(f"方法2结果: {getattr(result2, 'js_execution_result', 'No result')}")
                
            except Exception as e:
                logger.error(f"方法2失败: {e}")
            
            # 方法3: 检查是否有其他方式获取JavaScript结果
            logger.info("\n方法3: 检查其他属性")
            try:
                result3 = await crawler.arun(
                    url=test_url,
                    js_code="window.testResult = 'JavaScript executed'; return window.testResult;",
                    wait_for=3
                )
                
                # 检查所有可能包含结果的属性
                for attr in ['js_execution_result', 'js_result', 'execution_result', 'result']:
                    if hasattr(result3, attr):
                        value = getattr(result3, attr)
                        logger.info(f"  {attr}: {value}")
                
                # 检查HTML中是否有JavaScript执行的痕迹
                if result3.html and 'testResult' in result3.html:
                    logger.info("HTML中包含JavaScript执行痕迹")
                
            except Exception as e:
                logger.error(f"方法3失败: {e}")
            
            return True
            
    except Exception as e:
        logger.error(f"JavaScript执行测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_bank_page_with_simple_js():
    """测试银行页面的简单JavaScript执行"""
    
    logger.info("测试银行页面的简单JavaScript执行")
    
    list_page_url = (
        "https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html"
        "?_u=24&PrdTyp=A&behavior_entryid=LC0JGQ001&behavior_pageid=459C25E0"
        "&behavior_slotid=&appflag=1&_urlrefer=PLATFORM&DeviceType=E"
        "&Version=13.3.3&SystemVersion=11"
    )
    
    try:
        async with AsyncWebCrawler(verbose=True) as crawler:
            # 配置浏览器
            browser_config = {
                "headless": False,
                "viewport": {"width": 414, "height": 896},
                "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1"
            }
            
            # 简单的JavaScript代码
            js_code = """
            console.log('银行页面JavaScript开始执行');
            
            // 等待页面加载
            await new Promise(resolve => setTimeout(resolve, 10000));
            
            // 检查页面内容
            const hasFinance = document.body.innerText.includes('理财');
            const hasProducts = document.querySelectorAll('.product, .prd, .item').length;
            const hasButtons = document.querySelectorAll('button').length;
            
            console.log('页面检查结果:', {
                hasFinance: hasFinance,
                hasProducts: hasProducts,
                hasButtons: hasButtons
            });
            
            // 尝试滚动
            window.scrollTo(0, document.body.scrollHeight);
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 尝试点击第一个按钮
            const buttons = document.querySelectorAll('button');
            if (buttons.length > 0) {
                try {
                    buttons[0].click();
                    console.log('点击了第一个按钮');
                    await new Promise(resolve => setTimeout(resolve, 3000));
                } catch (e) {
                    console.log('点击按钮失败:', e);
                }
            }
            
            const result = {
                hasFinance: hasFinance,
                productElements: hasProducts,
                buttonCount: hasButtons,
                pageTitle: document.title,
                timestamp: new Date().toISOString()
            };
            
            console.log('最终结果:', result);
            return result;
            """
            
            logger.info("开始银行页面测试...")
            
            result = await crawler.arun(
                url=list_page_url,
                browser_config=browser_config,
                js_code=js_code,
                wait_for=20
            )
            
            logger.info("银行页面测试完成")
            
            # 保存调试信息
            debug_file = f"debug/bank_js_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            debug_data = {
                "timestamp": datetime.now().isoformat(),
                "url": list_page_url,
                "html_length": len(result.html) if result.html else 0,
                "js_execution_result": getattr(result, 'js_execution_result', None)
            }
            
            with open(debug_file, 'w', encoding='utf-8') as f:
                json.dump(debug_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"调试信息已保存到: {debug_file}")
            
            # 检查结果
            js_result = getattr(result, 'js_execution_result', None)
            if js_result:
                logger.info(f"JavaScript执行成功: {js_result}")
                return True
            else:
                logger.warning("JavaScript执行结果为空")
                return False
            
    except Exception as e:
        logger.error(f"银行页面JavaScript测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    logger.info("开始JavaScript执行测试")
    
    # 确保debug目录存在
    os.makedirs("debug", exist_ok=True)
    
    # 测试1: 基本JavaScript执行
    logger.info("=" * 50)
    success1 = await test_js_execution_methods()
    
    # 测试2: 银行页面JavaScript执行
    logger.info("=" * 50)
    success2 = await test_bank_page_with_simple_js()
    
    logger.info("=" * 50)
    logger.info("测试结果:")
    logger.info(f"基本JavaScript执行: {'✓ 成功' if success1 else '✗ 失败'}")
    logger.info(f"银行页面JavaScript执行: {'✓ 成功' if success2 else '✗ 失败'}")
    
    if success1 or success2:
        logger.info("JavaScript执行部分成功，可以继续开发网络监听功能")
    else:
        logger.error("JavaScript执行完全失败，需要检查crawl4ai配置")


if __name__ == "__main__":
    # 设置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:HH:mm:ss} | {level} | {message}")
    
    # 运行测试
    asyncio.run(main())
