# 银行理财产品爬虫系统

一个基于 crawl4ai 的银行理财产品信息爬取系统，支持自动化爬取招商银行理财产品列表、详情信息和历史收益数据。

## 功能特性

- 🚀 **自动化爬取**: 使用 crawl4ai 监听网络请求，自动获取理财产品数据
- 📊 **完整数据**: 支持爬取产品列表、详情信息和历史收益数据
- 💾 **数据存储**: 支持 SQLite/MySQL 数据库存储，自动保存 JSON 文件
- ⏰ **定时任务**: 内置调度器，支持定时自动爬取
- 📝 **日志监控**: 完整的日志记录和监控系统
- 🔧 **配置管理**: 灵活的配置管理，支持环境变量覆盖
- 🛡️ **错误处理**: 完善的错误处理和重试机制

## 系统架构

```
nm_crawl/
├── src/nm_crawl/
│   ├── config/          # 配置管理
│   ├── crawlers/        # 爬虫模块
│   ├── database/        # 数据库操作
│   ├── models/          # 数据模型
│   ├── utils/           # 工具模块
│   └── main.py          # 主程序入口
├── data/                # 数据存储目录
├── logs/                # 日志目录
├── tests/               # 测试文件
└── config.json          # 配置文件
```

## 快速开始

### 1. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd crawl_nm

# 安装依赖
pip install -e .
```

### 2. 初始化配置

```bash
# 创建默认配置文件
nm-crawl init-config
```

### 3. 运行爬虫

```bash
# 爬取产品列表
nm-crawl crawl-list

# 完整爬取（列表+详情）
nm-crawl crawl-full

# 启动定时调度器
nm-crawl start-scheduler

# 查看状态
nm-crawl status
```

## 配置说明

### 配置文件 (config.json)

```json
{
  "app_name": "nm_crawl",
  "version": "0.1.0",
  "debug": false,
  "database": {
    "url": "sqlite+aiosqlite:///data/financial_products.db",
    "echo": false
  },
  "crawler": {
    "list_wait_time": 20,
    "detail_wait_time": 15,
    "detail_delay": 3,
    "max_detail_products": 50,
    "max_retries": 3,
    "headless": true
  },
  "scheduler": {
    "enable_scheduler": false,
    "full_crawl_interval": "daily",
    "auto_start": false
  },
  "logging": {
    "level": "INFO",
    "log_directory": "logs",
    "enable_file_logging": true
  },
  "storage": {
    "data_directory": "data",
    "backup_enabled": true
  }
}
```

### 环境变量 (.env)

```bash
# 数据库配置
DATABASE_URL=sqlite+aiosqlite:///data/financial_products.db
DATABASE_ECHO=false

# 爬虫配置
CRAWLER_HEADLESS=true
CRAWLER_MAX_PRODUCTS=50

# 调度器配置
SCHEDULER_ENABLED=false
SCHEDULER_AUTO_START=false

# 日志配置
LOG_LEVEL=INFO
LOG_DIRECTORY=logs

# 存储配置
DATA_DIRECTORY=data

# 应用配置
DEBUG=false
```

## 命令行接口

### 基本命令

```bash
# 显示帮助
nm-crawl --help

# 使用指定配置文件
nm-crawl -c custom_config.json <command>

# 爬取产品列表
nm-crawl crawl-list [--wait-time 20] [--max-scroll 5]

# 完整爬取
nm-crawl crawl-full [--max-products 50]

# 启动调度器
nm-crawl start-scheduler

# 查看状态
nm-crawl status

# 初始化配置
nm-crawl init-config
```

### 高级用法

```bash
# 自定义等待时间爬取列表
nm-crawl crawl-list --wait-time 30 --max-scroll 10

# 限制详情爬取数量
nm-crawl crawl-full --max-products 20

# 使用自定义配置文件
nm-crawl -c production.json crawl-full
```

## 数据结构

### 理财产品表 (financial_products)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键 |
| rip_cod | String | 产品代码 |
| saa_cod | String | 销售代码 |
| rip_snm | String | 产品名称 |
| prd_rat | String | 产品收益率 |
| risk_lvl | String | 风险等级 |
| crp_nam | String | 公司名称 |
| ... | ... | 其他字段 |

### 历史收益表 (history_profits)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键 |
| product_id | Integer | 产品ID |
| date | String | 日期 |
| ten_thousand_profit | String | 万份收益 |
| seven_days_annual_profit | String | 七日年化收益率 |

## 开发指南

### 项目结构

- `crawlers/`: 爬虫核心模块
  - `list_crawler.py`: 列表页爬虫
  - `detail_crawler.py`: 详情页爬虫
  - `network_monitor.py`: 网络请求监听器

- `database/`: 数据库操作
  - `connection.py`: 数据库连接管理
  - `dao.py`: 数据访问对象

- `utils/`: 工具模块
  - `scheduler.py`: 任务调度器
  - `data_processor.py`: 数据处理器
  - `data_storage.py`: 数据存储服务
  - `logger.py`: 日志和监控

### 扩展开发

1. **添加新的银行支持**:
   - 继承 `NetworkRequestMonitor` 类
   - 实现特定银行的URL和数据解析逻辑

2. **自定义数据处理**:
   - 扩展 `DataProcessor` 类
   - 添加新的数据清洗和验证规则

3. **添加新的存储后端**:
   - 实现新的 DAO 类
   - 扩展 `DataStorageService`

## 监控和日志

### 日志文件

- `logs/app.log`: 应用主日志
- `logs/crawl/crawl.log`: 爬取专用日志
- `logs/error/error.log`: 错误日志
- `logs/monitor/`: 监控数据

### 监控指标

- 爬取会话数
- 成功/失败率
- 产品数量统计
- 错误和警告记录

## 故障排除

### 常见问题

1. **爬虫无法启动**
   - 检查 crawl4ai 依赖是否正确安装
   - 确认浏览器驱动是否可用

2. **数据库连接失败**
   - 检查数据库URL配置
   - 确认数据库文件权限

3. **爬取数据为空**
   - 检查目标网站是否可访问
   - 调整等待时间和重试参数

### 调试模式

```bash
# 启用调试模式
export DEBUG=true
nm-crawl crawl-list

# 查看详细日志
tail -f logs/debug/debug.log
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v0.1.0
- 初始版本发布
- 支持招商银行理财产品爬取
- 完整的配置管理和调度系统
