"""
通用表格数据处理框架 - 快速开始指南
演示如何在实际项目中使用框架
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.crawlers.detail_crawler import FinancialProductDetailCrawler
from src.nm_crawl.services.database_service import CmbDatabaseService
from src.nm_crawl.database.connection import init_database


async def example_1_basic_usage():
    """示例1: 基本使用方法"""
    print("=== 示例1: 基本使用方法 ===")
    
    # 1. 初始化数据库
    await init_database()
    print("✓ 数据库初始化完成")
    
    # 2. 创建数据库服务
    db_service = CmbDatabaseService()
    
    # 3. 检查支持的API
    supported_apis = await db_service.get_supported_chart_apis()
    print(f"✓ 支持的API: {supported_apis}")
    
    # 4. 模拟从API获取的数据
    profit_data = [
        {"date": "2024-01-01", "tenThousandProfit": "1.23", "sevenDaysAnnualProfit": "4.56"},
        {"date": "2024-01-02", "tenThousandProfit": "1.24", "sevenDaysAnnualProfit": "4.57"},
    ]
    
    netvalue_data = [
        {"date": "2024-01-01", "unitNetValue": "1.0123", "totalNetValue": "1.0123", "netValueChange": "0.0012"},
        {"date": "2024-01-02", "unitNetValue": "1.0135", "totalNetValue": "1.0135", "netValueChange": "0.0012"},
    ]
    
    # 5. 保存数据 - 使用通用方法
    rip_cod = "EXAMPLE001"
    crawl_time = datetime.now()
    
    # 保存历史收益数据
    profit_count = await db_service.save_chart_data("get-history-profit", rip_cod, profit_data, crawl_time)
    print(f"✓ 保存了 {profit_count} 条历史收益记录")
    
    # 保存历史净值数据
    netvalue_count = await db_service.save_chart_data("get-history-net-value", rip_cod, netvalue_data, crawl_time)
    print(f"✓ 保存了 {netvalue_count} 条历史净值记录")


async def example_2_crawler_integration():
    """示例2: 与爬虫集成"""
    print("\n=== 示例2: 与爬虫集成 ===")
    
    # 创建爬虫实例，启用数据库保存
    crawler = FinancialProductDetailCrawler(save_to_db=True)
    
    # 检查爬虫支持的API
    print(f"✓ 爬虫支持的chart_apis: {crawler.chart_apis}")
    
    # 模拟爬虫处理数据的过程
    rip_cod = "EXAMPLE002"
    
    # 模拟从网络请求中提取的数据
    chart_data_profit = [
        {"date": "2024-01-01", "tenThousandProfit": "1.25", "sevenDaysAnnualProfit": "4.58"},
        {"date": "2024-01-02", "tenThousandProfit": "1.26", "sevenDaysAnnualProfit": "4.59"},
    ]
    
    chart_data_netvalue = [
        {"date": "2024-01-01", "unitNetValue": "1.0145", "totalNetValue": "1.0145", "netValueChange": "0.0010"},
        {"date": "2024-01-02", "unitNetValue": "1.0155", "totalNetValue": "1.0155", "netValueChange": "0.0010"},
    ]
    
    # 模拟爬虫的数据保存逻辑
    if crawler.save_to_db:
        try:
            # 保存历史收益数据
            crawl_time = datetime.now()
            saved_count = await crawler.db_service.save_chart_data(
                "get-history-profit", rip_cod, chart_data_profit, crawl_time
            )
            print(f"✓ 爬虫保存了 {saved_count} 条历史收益记录")
            
            # 保存历史净值数据
            saved_count = await crawler.db_service.save_chart_data(
                "get-history-net-value", rip_cod, chart_data_netvalue, crawl_time
            )
            print(f"✓ 爬虫保存了 {saved_count} 条历史净值记录")
            
        except Exception as e:
            print(f"✗ 数据保存失败: {e}")


async def example_3_error_handling():
    """示例3: 错误处理和数据验证"""
    print("\n=== 示例3: 错误处理和数据验证 ===")
    
    db_service = CmbDatabaseService()
    
    # 测试不支持的API
    try:
        result = await db_service.save_chart_data("unsupported-api", "TEST", [], datetime.now())
        print(f"不支持的API处理结果: {result}")
    except Exception as e:
        print(f"✓ 正确处理了不支持的API: {e}")
    
    # 测试无效数据
    invalid_data = [
        {"tenThousandProfit": "1.23"},  # 缺少必填的date字段
        {"date": "", "tenThousandProfit": "1.24"},  # 空日期
    ]
    
    try:
        result = await db_service.save_chart_data("get-history-profit", "TEST", invalid_data, datetime.now())
        print(f"✓ 无效数据处理结果: 保存了 {result} 条记录（应该为0）")
    except Exception as e:
        print(f"无效数据处理异常: {e}")


async def example_4_data_query():
    """示例4: 数据查询"""
    print("\n=== 示例4: 数据查询 ===")
    
    from sqlalchemy import select
    from src.nm_crawl.database.connection import get_db_session
    from src.nm_crawl.models.financial_models import HistoryProfits, HistoryNetValue
    
    # 查询历史收益数据
    async for session in get_db_session():
        # 查询最近的收益记录
        stmt = select(HistoryProfits).order_by(HistoryProfits.profit_date.desc()).limit(5)
        result = await session.execute(stmt)
        profit_records = result.scalars().all()
        
        print(f"✓ 找到 {len(profit_records)} 条最新的历史收益记录:")
        for record in profit_records:
            print(f"  - {record.rip_cod} {record.profit_date}: 万份收益 {record.ten_thousand_profit}")
        
        # 查询最近的净值记录
        stmt = select(HistoryNetValue).order_by(HistoryNetValue.net_value_date.desc()).limit(5)
        result = await session.execute(stmt)
        netvalue_records = result.scalars().all()
        
        print(f"✓ 找到 {len(netvalue_records)} 条最新的历史净值记录:")
        for record in netvalue_records:
            print(f"  - {record.rip_cod} {record.net_value_date}: 单位净值 {record.unit_net_value}")
        
        break


def show_framework_benefits():
    """展示框架的优势"""
    print("\n=== 框架优势 ===")
    
    benefits = [
        "🚀 配置化: 通过简单配置即可添加新的表格类型",
        "🔧 通用性: 统一的数据处理接口，减少重复代码",
        "✅ 验证: 内置数据验证机制，确保数据质量",
        "🔄 转换: 自动进行数据类型转换和字段映射",
        "🛡️ 安全: 自动处理数据冲突和去重",
        "📊 扩展: 易于扩展，支持复杂的数据处理需求",
        "🐛 调试: 详细的日志记录，便于问题排查",
        "⚡ 性能: 批量处理和数据库优化"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")


def show_next_steps():
    """展示后续步骤"""
    print("\n=== 后续步骤 ===")
    
    steps = [
        "1. 根据需要添加新的表格类型（参考 examples/add_new_table_example.py）",
        "2. 在爬虫的 chart_apis 列表中添加新的API名称",
        "3. 运行爬虫，数据会自动保存到对应的数据库表",
        "4. 使用标准的SQLAlchemy查询来访问数据",
        "5. 根据业务需求添加更多的数据验证和转换规则"
    ]
    
    for step in steps:
        print(f"  {step}")


async def main():
    """主函数"""
    print("通用表格数据处理框架 - 快速开始指南")
    print("=" * 50)
    
    # 运行所有示例
    await example_1_basic_usage()
    await example_2_crawler_integration()
    await example_3_error_handling()
    await example_4_data_query()
    
    # 显示框架优势和后续步骤
    show_framework_benefits()
    show_next_steps()
    
    print("\n🎉 快速开始指南完成！")
    print("现在您可以开始使用通用表格数据处理框架了。")


if __name__ == "__main__":
    asyncio.run(main())
