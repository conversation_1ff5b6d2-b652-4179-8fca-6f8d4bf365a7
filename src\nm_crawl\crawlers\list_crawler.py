"""
Financial Product List Crawler
"""

import json
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

import requests
from crawl4ai import Async<PERSON>ebCrawler, CrawlerRunConfig, CacheMode, BrowserConfig
from loguru import logger
import aiofiles
import os


class FinancialProductListCrawler:
    """理财产品列表爬虫"""

    def __init__(self, cdp_url: str = None, save_directory: str = "data"):
        """
        初始化网络监听爬虫

        Args:
            cdp_url: cdp_url
            save_directory: 数据保存目录
        """
        self.session_id = f"network_listener_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        self.cdp_url = cdp_url
        self.save_directory = save_directory
        self.requests_dir = os.path.join(save_directory, "requests")
        self.products_dir = os.path.join(save_directory, "products")

        # 确保目录存在
        os.makedirs(self.requests_dir, exist_ok=True)
        os.makedirs(self.products_dir, exist_ok=True)

        # 招商银行理财产品列表页URL
        self.list_page_url = (
            "https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html"
            "?_u=24&PrdTyp=A&behavior_entryid=LC0JGQ001&behavior_pageid=459C25E0"
            "&behavior_slotid=&appflag=1&_urlrefer=PLATFORM&DeviceType=E"
            "&Version=13.3.3&SystemVersion=11"
        )

        # 目标API接口
        self.target_apis = [
            "cashlistnew",  # 活钱管理
            "bcdlist",  # 稳健低波
            # "financelist",
            # "productlist"
        ]

        # 存储捕获的数据
        self.captured_requests = []
        self.all_products = []

        logger.info("FinancialProductListCrawler initialized")

    async def _save_json_data(self, data: Any, filename: str, directory: str = None) -> str:
        """
        保存JSON数据到文件

        Args:
            data: 要保存的数据
            filename: 文件名
            directory: 保存目录

        Returns:
            str: 保存的文件路径
        """
        if directory is None:
            directory = self.products_dir

        filepath = os.path.join(directory, filename)

        try:
            async with aiofiles.open(filepath, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(data, ensure_ascii=False, indent=2))

            logger.info(f"Saved data to: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Failed to save data to {filepath}: {e}")
            raise

    async def _write_file(self, data: Any, filename: str, directory: str = None) -> str:
        """
        保存JSON数据到文件

        Args:
            data: 要保存的数据
            filename: 文件名
            directory: 保存目录

        Returns:
            str: 保存的文件路径
        """
        if data is None:
            logger.error(f"{filename}:No content to write.")
            return ""

        if directory is None:
            directory = self.products_dir

        filepath = os.path.join(directory, filename)

        try:
            async with aiofiles.open(filepath, 'w', encoding='utf-8') as f:
                await f.write(data)

            logger.info(f"Saved data to: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Failed to save data to {filepath}: {e}")
            raise

    def _is_target_request(self, url: str) -> bool:
        """
        检查是否为目标请求URL

        Args:
            url: 请求URL

        Returns:
            bool: 是否为目标请求
        """
        return any(api in url.lower() for api in self.target_apis)

    async def _extract_products_from_response(self, response_text: str) -> List[Dict[str, Any]]:
        """
        从响应文本中提取产品数据

        Args:
            response_text: 响应文本

        Returns:
            List[Dict]: 产品列表
        """
        try:
            data = json.loads(response_text)

            # 处理不同的响应格式
            if data.get("sysCode") in [200, 1014] and "bizResult" in data:
                biz_result = data["bizResult"]
                if biz_result.get("code") == 200 and "data" in biz_result:
                    prd_list = biz_result["data"].get("prdList", [])
                    logger.info(f"Extracted {len(prd_list)} products from response")
                    return prd_list

            logger.warning("No valid product data found in response")
            return []

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            return []
        except Exception as e:
            logger.error(f"Failed to extract products from response: {e}")
            return []

    async def crawl_product_list(self, wait_time: int = 20, attempts: int = 20) -> List[Dict[str, Any]]:
        """
        使用网络监听功能爬取产品列表

        Args:
            wait_time: 等待时间（秒）

        Returns:
            List[Dict]: 产品列表数据
        """
        logger.info("Starting crawl with network listener")

        # 清空之前的数据
        self.captured_requests.clear()
        self.all_products.clear()

        # 配置浏览器
        browser_config = BrowserConfig(
            cdp_url=self.cdp_url,
            headless=False,  # 显示浏览器以便调试
            java_script_enabled=True,

            # viewport_width=414,
            # viewport_height=896,
            # user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
            # extra_args=[
            #     "--no-sandbox",
            #     "--disable-dev-shm-usage",
            #     "--disable-blink-features=AutomationControlled",
            #     "--disable-web-security"
            # ]
        )

        # 配置爬取参数
        crawler_config = CrawlerRunConfig(
            session_id=self.session_id,
            cache_mode=CacheMode.BYPASS,

            # page_timeout=60 * 60 * 1000,  # 1 hour
            delay_before_return_html=3.0,
            # 启用网络监听
            capture_network_requests=True,
            wait_for="""js:() => {
                function isCurrentTabAtBottom() {
                    // 1. 找到当前激活的 Tab 对应的内容区域
                    const activeSlide = document.querySelector('.swiper-slide.list[aria-hidden="false"]');
                    if (!activeSlide) return false;
                
                    // 2. 在当前 Tab 内查找“已经到底了”提示
                    const bottomTip = activeSlide.querySelector('.ef_bottom_tip');
                    if (!bottomTip) return false;
                
                    // 3. 检查文本和可见性
                    const isVisible = bottomTip.offsetParent !== null;
                    const hasCorrectText = bottomTip.textContent.trim() === '已经到底了';
                
                    return isVisible && hasCorrectText;
                }
                if(isCurrentTabAtBottom()){
                    return true;
                }
                return document.querySelectorAll('.prd-list').length > 10;
            }""",
            # 过滤网络请求
            # network_request_filter=lambda url: self._is_target_request(url),
            # JavaScript代码用于模拟用户行为
            # js_code="""
            # console.log('页面加载完成，开始模拟用户行为');
            #
            # // 等待页面完全加载
            # await new Promise(resolve => setTimeout(resolve, 3000));
            #
            # // 模拟用户滚动
            # console.log('开始滚动页面');
            # for (let i = 0; i < 3; i++) {
            #     window.scrollTo(0, document.body.scrollHeight);
            #     await new Promise(resolve => setTimeout(resolve, 2000));
            #
            #     // 滚动回顶部
            #     window.scrollTo(0, 0);
            #     await new Promise(resolve => setTimeout(resolve, 1000));
            # }
            #
            # // 尝试点击可能的标签或按钮
            # console.log('尝试点击页面元素');
            # const clickableElements = document.querySelectorAll('button, .tab, .swiper-slide, [role="button"]');
            #
            # for (let i = 0; i < Math.min(clickableElements.length, 3); i++) {
            #     try {
            #         const element = clickableElements[i];
            #         const text = element.textContent || element.innerText || '';
            #         console.log(`点击元素: ${text}`);
            #
            #         element.click();
            #         await new Promise(resolve => setTimeout(resolve, 3000));
            #     } catch (e) {
            #         console.log('点击失败:', e);
            #     }
            # }
            #
            # // 最后再次滚动
            # console.log('最后滚动');
            # window.scrollTo(0, document.body.scrollHeight);
            # await new Promise(resolve => setTimeout(resolve, 3000));
            #
            # console.log('用户行为模拟完成');
            # return 'completed';
            # """
            js_code="""            
            await new Promise(resolve => setTimeout(resolve, 3000));
            document.querySelector(".downloading").scrollIntoView(false);
            """,
            simulate_user=True,
        )

        try:
            async with AsyncWebCrawler(config=browser_config) as crawler:
                logger.info(f"Starting to crawl: {self.list_page_url}")

                result = await crawler.arun(
                    url=self.list_page_url,
                    config=crawler_config,
                )

                logger.info("Crawling completed, processing network requests")

                # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                # file = f"product_list_{timestamp}"
                # await self._write_file(f"{file}.html", result.html)
                # await self._write_file(f"{file}.cleaned.html", result.cleaned_html)
                # await self._write_file(f"{file}.fit.html", result.fit_html)
                # await self._write_file(f"{file}.mhtml.html", result.mhtml)

                # 处理捕获的网络请求
                if hasattr(result, 'network_requests') and result.network_requests:
                    logger.info(f"Captured {len(result.network_requests)} network requests")

                    await self._process_network_requests(result.network_requests)

                    # for request in result.network_requests:
                    #     await self._process_network_request(request)
                else:
                    logger.warning("No network requests captured")

                # 如果没有通过网络请求获取到数据，尝试从页面内容中提取
                # todo： 待测试
                # if not self.all_products:
                #     logger.info("No products from network requests, trying to extract from page content")
                #     await self._extract_from_page_content(result.html)

                # 保存结果
                if self.all_products:
                    await self._save_crawl_results()

                return self.all_products

        except Exception as e:
            logger.error(f"Crawling failed: {e}")
            import traceback
            traceback.print_exc()
            return []

    async def _process_network_requests(self, network_events: List[Dict[str, Any]]):
        """
        处理单个网络请求

        Args:
            network_events: 网络请求数据
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        request_filename = f"network_events_list_{timestamp}.json"
        await self._save_json_data(network_events, request_filename, self.requests_dir)

        requests = []
        responses = []
        failures = []

        for event in network_events:
            event_type = event.get("event_type")
            if event_type == "request":
                requests.append(event)
            elif event_type == "response":
                if event.get("headers") and event.get("headers").get("content-type", "").startswith("application/json"):
                    responses.append(event)
            elif event_type == "request_failed":
                failures.append(event)

        print(f"Captured {len(requests)} requests, {len(responses)} responses, and {len(failures)} failures")

        # 保存请求数据
        # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        # request_filename = f"network_responses_{timestamp}.json"
        # await self._save_json_data(responses, request_filename, self.requests_dir)

        try:
            for res in responses:
                url = res.get('url', '')
                status_code = res.get('status', 0)
                status_text = res.get('status_text', '')

                logger.info(f"Processing request: {url} (status: {status_code}-{status_text})")

                # 如果是目标API且有响应内容
                if self._is_target_request(url) and status_code == 200:
                    response_body = res['body']['text']

                    logger.info(f"Found target API response: {url}")

                    # 提取产品数据
                    products = await self._extract_products_from_response(response_body)
                    if products:
                        self.all_products.extend(products)
                        logger.info(f"Added {len(products)} products from {url}")

        except Exception as e:
            logger.error(f"Failed to process network request: {e}")

    async def _process_network_request(self, request: Dict[str, Any]):
        """
        处理单个网络请求
        无效
        Args:
            request: 网络请求数据
        """
        try:
            url = request.get('url', '')
            response_body = request.get('response_body', '')
            status_code = request.get('status_code', 0)

            logger.info(f"Processing request: {url} (status: {status_code})")

            # 保存请求数据
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            request_filename = f"network_request_{timestamp}.json"
            await self._save_json_data(request, request_filename, self.requests_dir)

            # 如果是目标API且有响应内容
            if self._is_target_request(url) and response_body and status_code == 200:
                logger.info(f"Found target API response: {url}")

                # 提取产品数据
                products = await self._extract_products_from_response(response_body)
                if products:
                    self.all_products.extend(products)
                    logger.info(f"Added {len(products)} products from {url}")

        except Exception as e:
            logger.error(f"Failed to process network request: {e}")

    async def _extract_from_page_content(self, html_content: str):
        """
        从页面内容中提取产品信息（备用方法）

        Args:
            html_content: 页面HTML内容
        """
        try:
            from bs4 import BeautifulSoup

            soup = BeautifulSoup(html_content, 'html.parser')

            # 查找可能包含产品信息的脚本标签
            scripts = soup.find_all('script')

            for script in scripts:
                if script.string:
                    script_content = script.string

                    # 查找可能的产品数据
                    if 'prdList' in script_content or 'ripSnm' in script_content:
                        logger.info("Found potential product data in script tag")

                        # 尝试提取JSON数据
                        import re
                        json_matches = re.findall(r'\{[^{}]*prdList[^{}]*\}', script_content)

                        for match in json_matches:
                            try:
                                data = json.loads(match)
                                products = await self._extract_products_from_response(json.dumps(data))
                                if products:
                                    self.all_products.extend(products)
                                    logger.info(f"Extracted {len(products)} products from page script")
                            except:
                                continue

        except Exception as e:
            logger.error(f"Failed to extract from page content: {e}")

    async def _save_crawl_results(self):
        """保存爬取结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 去重产品数据
        unique_products = []
        seen_products = set()

        for product in self.all_products:
            rip_cod = product.get("ripCod")
            saa_cod = product.get("saaCod")

            if rip_cod and saa_cod:
                key = f"{rip_cod}_{saa_cod}"
                if key not in seen_products:
                    seen_products.add(key)
                    unique_products.append(product)

        self.all_products = unique_products

        # 保存产品列表
        if self.all_products:
            filename = f"product_list_network_{timestamp}.json"
            await self._save_json_data({
                "products": self.all_products,
                "total_count": len(self.all_products),
                "crawl_time": datetime.now().isoformat(),
                "source": "network_listener",
                "method": "crawl4ai network monitoring"
            }, filename)

    async def get_products(self) -> List[Dict[str, Any]]:
        """
        获取爬取的产品列表

        Returns:
            List[Dict]: 产品列表
        """
        return self.all_products.copy()

    async def get_product_codes(self) -> List[tuple]:
        """
        获取产品代码列表

        Returns:
            List[tuple]: (rip_cod, saa_cod) 元组列表
        """
        codes = []
        for product in self.all_products:
            rip_cod = product.get("ripCod")
            saa_cod = product.get("saaCod")
            if rip_cod and saa_cod:
                codes.append((rip_cod, saa_cod))
        return codes
