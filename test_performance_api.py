"""
测试 get-history-performance API 的数据处理
验证新添加的历史业绩数据功能
"""

import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.config.table_config import get_table_config, table_config_manager
from src.nm_crawl.services.database_service import CmbDatabaseService
from src.nm_crawl.database.connection import init_database
from src.nm_crawl.models.financial_models import HistoryPerformance
from sqlalchemy import select
from src.nm_crawl.database.connection import get_db_session
from loguru import logger


async def test_performance_config():
    """测试业绩数据配置"""
    print("=== 测试业绩数据配置 ===")
    
    # 检查配置是否存在
    performance_config = get_table_config("get-history-performance")
    if performance_config:
        print(f"✓ 找到业绩数据配置: {performance_config.api_name}")
        print(f"  - 表名: {performance_config.table_name}")
        print(f"  - 模型类: {performance_config.model_class.__name__}")
        print(f"  - 唯一字段: {performance_config.unique_fields}")
        print(f"  - 字段映射数量: {len(performance_config.field_mappings)}")
        
        for mapping in performance_config.field_mappings:
            print(f"    {mapping.source_field} -> {mapping.target_field} (required: {mapping.required})")
        
        return True
    else:
        print("✗ 未找到业绩数据配置")
        return False


async def test_performance_data_processing():
    """测试业绩数据处理"""
    print("\n=== 测试业绩数据处理 ===")
    
    # 模拟您提供的API响应数据
    api_response = {
        "sysCode": 200,
        "sysMsg": "Success",
        "bizResult": {
            "code": 200,
            "data": {
                "list": [
                    {
                        "prfTyp": "A",
                        "timeInterval": "近1月",
                        "netValueChange": "0.23",
                        "yeaYld": "2.68"
                    },
                    {
                        "prfTyp": "B",
                        "timeInterval": "近3月",
                        "netValueChange": "0.75",
                        "yeaYld": "2.96"
                    },
                    {
                        "prfTyp": "J",
                        "timeInterval": "今年以来",
                        "netValueChange": "1.36",
                        "yeaYld": "3.33"
                    },
                    {
                        "prfTyp": "F",
                        "timeInterval": "成立以来",
                        "netValueChange": "1.36",
                        "yeaYld": "3.33"
                    }
                ],
                "yearList": [],
                "yldSwh": "Y",
                "zdfSwh": "Y",
                "nvcShw": "Y"
            }
        }
    }
    
    # 提取业绩数据列表
    performance_data = api_response["bizResult"]["data"]["list"]
    print(f"✓ 提取到 {len(performance_data)} 条业绩数据")
    
    # 显示数据内容
    for i, item in enumerate(performance_data):
        print(f"  数据 {i+1}: {item}")
    
    # 初始化数据库服务
    db_service = CmbDatabaseService()
    
    # 测试数据保存
    rip_cod = "TEST_PERFORMANCE_001"
    crawl_time = datetime.now()
    
    try:
        saved_count = await db_service.save_chart_data(
            "get-history-performance", 
            rip_cod, 
            performance_data, 
            crawl_time
        )
        print(f"✓ 成功保存 {saved_count} 条业绩记录")
        return True, saved_count
        
    except Exception as e:
        print(f"✗ 业绩数据处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False, 0


async def verify_performance_data_in_database(rip_cod: str):
    """验证业绩数据是否正确写入数据库"""
    print(f"\n=== 验证数据库中的业绩数据 (产品: {rip_cod}) ===")
    
    async for session in get_db_session():
        try:
            # 查询该产品的业绩数据
            stmt = select(HistoryPerformance).where(
                HistoryPerformance.rip_cod == rip_cod
            ).order_by(HistoryPerformance.prf_typ)
            
            result = await session.execute(stmt)
            records = result.scalars().all()
            
            print(f"✓ 数据库中找到 {len(records)} 条业绩记录")
            
            if records:
                print("详细记录:")
                for i, record in enumerate(records):
                    print(f"  记录 {i+1}:")
                    print(f"    产品ID: {record.product_id}")
                    print(f"    业绩类型: {record.prf_typ}")
                    print(f"    时间区间: {record.time_interval}")
                    print(f"    净值变化: {record.net_value_change}")
                    print(f"    年化收益: {record.yea_yld}")
                    print(f"    创建时间: {record.created_at}")
                    print(f"    爬取时间: {record.crawl_time}")
                    if record.raw_data:
                        print(f"    原始数据: {record.raw_data}")
                    print()
            
            # 统计总数
            total_count = len(records)
            print(f"✓ 该产品总共有 {total_count} 条业绩记录")
            
            return total_count
            
        except Exception as e:
            print(f"✗ 查询业绩数据失败: {e}")
            return 0
        
        break


async def test_api_support():
    """测试API支持检查"""
    print("\n=== 测试API支持检查 ===")
    
    db_service = CmbDatabaseService()
    
    # 检查支持的API列表
    supported_apis = await db_service.get_supported_chart_apis()
    print(f"✓ 支持的API列表: {supported_apis}")
    
    # 检查特定API是否支持
    test_apis = [
        "get-history-profit",
        "get-history-net-value", 
        "get-history-performance",
        "unknown-api"
    ]
    
    for api in test_apis:
        is_supported = await db_service.is_chart_api_supported(api)
        status = "✓" if is_supported else "✗"
        print(f"{status} {api}: {'支持' if is_supported else '不支持'}")


async def test_data_validation():
    """测试数据验证功能"""
    print("\n=== 测试数据验证功能 ===")
    
    db_service = CmbDatabaseService()
    
    # 测试无效数据
    invalid_data = [
        {
            # 缺少必填的prfTyp字段
            "timeInterval": "近1月",
            "netValueChange": "0.23",
            "yeaYld": "2.68"
        },
        {
            "prfTyp": "A",
            # 缺少必填的timeInterval字段
            "netValueChange": "0.75",
            "yeaYld": "2.96"
        },
        {
            "prfTyp": "",  # 空值
            "timeInterval": "今年以来",
            "netValueChange": "1.36",
            "yeaYld": "3.33"
        }
    ]
    
    try:
        saved_count = await db_service.save_chart_data(
            "get-history-performance", 
            "TEST_INVALID", 
            invalid_data, 
            datetime.now()
        )
        print(f"✓ 无效数据处理结果: 保存了 {saved_count} 条记录（应该为0）")
        
    except Exception as e:
        print(f"无效数据处理异常: {e}")


async def test_crawler_integration():
    """测试爬虫集成"""
    print("\n=== 测试爬虫集成 ===")
    
    from src.nm_crawl.crawlers.detail_crawler import FinancialProductDetailCrawler
    
    # 创建爬虫实例
    crawler = FinancialProductDetailCrawler(save_to_db=True)
    
    # 检查爬虫是否支持新的API
    print(f"✓ 爬虫支持的chart_apis: {crawler.chart_apis}")
    
    if "get-history-performance" in crawler.chart_apis:
        print("✓ 爬虫已支持 get-history-performance API")
        return True
    else:
        print("✗ 爬虫尚未支持 get-history-performance API")
        return False


async def main():
    """主测试函数"""
    print("开始测试 get-history-performance API 功能...")
    print("=" * 60)
    
    try:
        # 初始化数据库
        await init_database()
        print("✓ 数据库初始化完成")
        
        # 运行各项测试
        config_ok = await test_performance_config()
        if not config_ok:
            print("✗ 配置测试失败，停止后续测试")
            return
        
        processing_ok, saved_count = await test_performance_data_processing()
        if processing_ok and saved_count > 0:
            await verify_performance_data_in_database("TEST_PERFORMANCE_001")
        
        await test_api_support()
        await test_data_validation()
        crawler_ok = await test_crawler_integration()
        
        # 总结
        print("\n" + "=" * 60)
        print("测试总结:")
        
        if config_ok and processing_ok and crawler_ok:
            print("🎉 所有测试通过！get-history-performance API 已成功集成。")
            print("\n现在您可以：")
            print("1. 在爬虫中自动处理 get-history-performance 数据")
            print("2. 数据会自动保存到 history_performance 表")
            print("3. 支持数据去重和更新")
            print("4. 包含完整的数据验证机制")
        else:
            print("❌ 部分测试失败，请检查配置和代码")
            
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
