"""
Database connection and session management
"""

import os
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker
from loguru import logger

from ..models.financial_models import Base


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, database_url: str = None):
        """
        初始化数据库管理器
        
        Args:
            database_url: 数据库连接URL，默认使用SQLite
        """
        if database_url is None:
            # 默认使用SQLite数据库
            db_path = os.path.join(os.getcwd(), "data", "financial_products.db")
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            database_url = f"sqlite+aiosqlite:///{db_path}"
        
        self.database_url = database_url
        self.engine = create_async_engine(
            database_url,
            echo=False,  # 设置为True可以看到SQL语句
            future=True
        )
        
        # 创建异步会话工厂
        self.async_session_factory = async_sessionmaker(
            bind=self.engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        logger.info(f"Database initialized with URL: {database_url}")
    
    async def create_tables(self):
        """创建所有表"""
        try:
            async with self.engine.begin() as conn:
                # 创建数据库表结构
                await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create database tables: {e}")
            raise
    
    async def drop_tables(self):
        """删除所有表"""
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.drop_all)
            logger.info("Database tables dropped successfully")
        except Exception as e:
            logger.error(f"Failed to drop database tables: {e}")
            raise
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取数据库会话"""
        session = self.async_session_factory()
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            await session.close()
    
    async def close(self):
        """关闭数据库连接"""
        await self.engine.dispose()
        logger.info("Database connection closed")


# 全局数据库管理器实例
db_manager = None


async def init_database(database_url: str = None) -> DatabaseManager:
    """
    初始化数据库
    
    Args:
        database_url: 数据库连接URL
        
    Returns:
        DatabaseManager: 数据库管理器实例
    """
    global db_manager
    
    if db_manager is None:
        db_manager = DatabaseManager(database_url)
        await db_manager.create_tables()
    
    return db_manager


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话的便捷函数
    
    Returns:
        AsyncSession: 数据库会话
    """
    if db_manager is None:
        raise RuntimeError("Database not initialized. Call init_database() first.")
    
    async for session in db_manager.get_session():
        yield session


async def close_database():
    """关闭数据库连接"""
    global db_manager
    
    if db_manager is not None:
        await db_manager.close()
        db_manager = None
