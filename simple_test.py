#!/usr/bin/env python3
"""
Simple test to check basic crawl4ai functionality
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from crawl4ai import AsyncWebCrawler
from loguru import logger


async def test_basic_crawl():
    """测试基本的爬取功能"""
    
    # 招商银行理财产品列表页URL
    test_url = (
        "https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html"
        "?_u=24&PrdTyp=A&behavior_entryid=LC0JGQ001&behavior_pageid=459C25E0"
        "&behavior_slotid=&appflag=1&_urlrefer=PLATFORM&DeviceType=E"
        "&Version=13.3.3&SystemVersion=11"
    )
    
    logger.info(f"测试访问: {test_url}")
    
    try:
        async with Async<PERSON>ebCrawler(verbose=True) as crawler:
            # 基本配置
            browser_config = {
                "headless": False,  # 显示浏览器
                "args": [
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1"
                ]
            }
            
            # 简单的JavaScript测试
            simple_js = """
            console.log('JavaScript执行测试');
            
            // 等待页面加载
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // 检查页面内容
            const title = document.title;
            const bodyText = document.body ? document.body.innerText.substring(0, 500) : 'No body';
            const scripts = document.querySelectorAll('script').length;
            
            console.log('页面标题:', title);
            console.log('页面内容长度:', bodyText.length);
            console.log('脚本数量:', scripts);
            
            // 查找可能的API请求
            const links = Array.from(document.querySelectorAll('a')).map(a => a.href);
            const buttons = Array.from(document.querySelectorAll('button')).map(btn => btn.textContent);
            
            return {
                title: title,
                bodyText: bodyText,
                scripts: scripts,
                links: links.slice(0, 10),  // 只返回前10个链接
                buttons: buttons.slice(0, 10)  // 只返回前10个按钮
            };
            """
            
            logger.info("开始爬取...")
            
            result = await crawler.arun(
                url=test_url,
                wait_for=10,
                browser_config=browser_config,
                js_code=simple_js
            )
            
            logger.info("爬取完成")
            
            # 分析结果
            logger.info(f"HTML长度: {len(result.html) if result.html else 0}")
            logger.info(f"Markdown长度: {len(result.markdown) if result.markdown else 0}")
            
            if hasattr(result, 'js_execution_result') and result.js_execution_result:
                js_result = result.js_execution_result
                logger.info("JavaScript执行结果:")
                logger.info(f"- 页面标题: {js_result.get('title', 'N/A')}")
                logger.info(f"- 页面内容长度: {len(js_result.get('bodyText', ''))}")
                logger.info(f"- 脚本数量: {js_result.get('scripts', 0)}")
                logger.info(f"- 链接数量: {len(js_result.get('links', []))}")
                logger.info(f"- 按钮数量: {len(js_result.get('buttons', []))}")
                
                # 显示一些按钮文本
                buttons = js_result.get('buttons', [])
                if buttons:
                    logger.info("按钮文本:")
                    for i, btn_text in enumerate(buttons[:5]):
                        logger.info(f"  {i+1}. {btn_text}")
                
            else:
                logger.warning("没有JavaScript执行结果")
            
            # 保存HTML内容用于分析
            if result.html:
                os.makedirs("debug", exist_ok=True)
                html_file = "debug/page_content.html"
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(result.html)
                logger.info(f"页面HTML已保存到: {html_file}")
            
            return result
            
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


async def test_direct_api():
    """测试直接访问API"""
    import httpx
    
    api_url = "https://mobile.cmbchina.com/ientrustfinance/financelist/cashlistnew"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Referer': 'https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html'
    }
    
    logger.info(f"测试直接API访问: {api_url}")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(api_url, headers=headers, timeout=10)
            
            logger.info(f"API响应状态: {response.status_code}")
            logger.info(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    logger.info("API响应成功，数据结构:")
                    logger.info(f"- sysCode: {data.get('sysCode')}")
                    logger.info(f"- sysMsg: {data.get('sysMsg')}")
                    
                    if 'bizResult' in data:
                        biz_result = data['bizResult']
                        logger.info(f"- bizResult.code: {biz_result.get('code')}")
                        
                        if 'data' in biz_result and 'prdList' in biz_result['data']:
                            products = biz_result['data']['prdList']
                            logger.info(f"- 产品数量: {len(products)}")
                            
                            if products:
                                logger.info("前3个产品:")
                                for i, product in enumerate(products[:3], 1):
                                    logger.info(f"  {i}. {product.get('ripSnm', '未知')} - {product.get('prdRat', 'N/A')}")
                                return products
                        
                except Exception as e:
                    logger.error(f"解析JSON失败: {e}")
                    logger.info(f"原始响应: {response.text[:500]}...")
            else:
                logger.warning(f"API请求失败: {response.status_code}")
                logger.info(f"响应内容: {response.text[:500]}...")
                
    except Exception as e:
        logger.error(f"API测试失败: {e}")
    
    return []


async def main():
    """主函数"""
    logger.info("开始测试")
    
    # 测试1: 基本页面爬取
    logger.info("=" * 50)
    logger.info("测试1: 基本页面爬取")
    result = await test_basic_crawl()
    
    # 测试2: 直接API访问
    logger.info("=" * 50)
    logger.info("测试2: 直接API访问")
    products = await test_direct_api()
    
    if products:
        logger.info(f"✓ 直接API访问成功，获取到 {len(products)} 个产品")
    else:
        logger.warning("✗ 直接API访问失败")
    
    logger.info("=" * 50)
    logger.info("测试完成")


if __name__ == "__main__":
    # 设置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:HH:mm:ss} | {level} | {message}")
    
    # 运行测试
    asyncio.run(main())
