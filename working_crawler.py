
"""
Working crawler based on successful API discovery
"""

import asyncio
import httpx
import json
from datetime import datetime

async def crawl_financial_products():
    """可工作的理财产品爬虫"""
    
    api_url = "https://mobile.cmbchina.com/ientrustfinance/financelist/cashlistnew"
    params = {
        "prdTyp": "A",
        "ccyNbr": "",
        "slfTag": "",
        "bblTyp": "",
        "evlLvl": "",
        "trnCst": "",
        "prdFrm": "",
        "buyNf": "",
        "hotFlg": "",
        "ngtMkt": "",
        "rseFrm": "",
        "timTmp": int(datetime.now().timestamp() * 1000),  # 动态时间戳
        "yRipCod": "",
        "ySaaCod": "",
        "yDalCod": "",
        "yPagCnt": "",
        "prdInfoYS": []
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Type': 'application/json;charset=UTF-8',
        'Referer': 'https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html',
        'Origin': 'https://mobile.cmbchina.com',
        'X-Requested-With': 'XMLHttpRequest'
    }
    
    async with httpx.AsyncClient(timeout=30) as client:
        response = await client.post(api_url, json=params, headers=headers)

        if response.status_code == 200:
            data = response.json()

            # sysCode 1014 也表示成功
            if data.get('sysCode') in [200, 1014]:
                biz_result = data.get('bizResult', {})

                if biz_result.get('code') == 200:
                    prd_list = biz_result.get('data', {}).get('prdList', [])
                    return prd_list

    return []

# 使用示例
if __name__ == "__main__":
    products = asyncio.run(crawl_financial_products())
    print(f"获取到 {len(products)} 个产品")
    for i, product in enumerate(products[:3], 1):
        print(f"{i}. {product.get('ripSnm', '未知')} - {product.get('prdRat', 'N/A')}")
