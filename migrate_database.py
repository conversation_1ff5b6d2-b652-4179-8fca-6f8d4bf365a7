#!/usr/bin/env python3
"""
数据库迁移脚本 - 为history_profits表添加净值相关字段
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.database.connection import init_database, close_database, get_db_session
from sqlalchemy import text
from loguru import logger


async def check_table_structure():
    """检查当前表结构"""
    logger.info("Checking current table structure...")
    
    async for session in get_db_session():
        try:
            # 查询表结构
            result = await session.execute(text("PRAGMA table_info(history_profits)"))
            columns = result.fetchall()
            
            logger.info("Current history_profits table structure:")
            for column in columns:
                logger.info(f"  {column[1]} {column[2]} {'NOT NULL' if column[3] else 'NULL'}")
            
            # 检查是否已有新字段
            column_names = [col[1] for col in columns]
            new_fields = ['unit_net_value', 'total_net_value', 'net_value_change', 'show_provision', 'data_type']
            missing_fields = [field for field in new_fields if field not in column_names]
            
            return missing_fields
            
        except Exception as e:
            logger.error(f"Failed to check table structure: {e}")
            return []


async def add_missing_columns(missing_fields):
    """添加缺失的列"""
    if not missing_fields:
        logger.info("All required columns already exist")
        return True
    
    logger.info(f"Adding missing columns: {missing_fields}")
    
    async for session in get_db_session():
        try:
            # 定义新字段的SQL
            column_definitions = {
                'unit_net_value': 'VARCHAR(20)',
                'total_net_value': 'VARCHAR(20)',
                'net_value_change': 'VARCHAR(20)',
                'show_provision': 'BOOLEAN',
                'data_type': 'VARCHAR(20)'
            }
            
            # 添加每个缺失的列
            for field in missing_fields:
                if field in column_definitions:
                    sql = f"ALTER TABLE history_profits ADD COLUMN {field} {column_definitions[field]}"
                    logger.info(f"Executing: {sql}")
                    await session.execute(text(sql))
            
            await session.commit()
            logger.info("✅ Successfully added missing columns")
            return True
            
        except Exception as e:
            await session.rollback()
            logger.error(f"Failed to add columns: {e}")
            return False


async def verify_migration():
    """验证迁移结果"""
    logger.info("Verifying migration...")
    
    async for session in get_db_session():
        try:
            # 查询更新后的表结构
            result = await session.execute(text("PRAGMA table_info(history_profits)"))
            columns = result.fetchall()
            
            logger.info("Updated history_profits table structure:")
            for column in columns:
                logger.info(f"  {column[1]} {column[2]} {'NOT NULL' if column[3] else 'NULL'}")
            
            # 检查所有必需字段是否存在
            column_names = [col[1] for col in columns]
            required_fields = [
                'id', 'product_id', 'rip_cod', 'profit_date',
                'ten_thousand_profit', 'seven_days_annual_profit',
                'unit_net_value', 'total_net_value', 'net_value_change', 
                'show_provision', 'data_type', 'created_at', 'crawl_time'
            ]
            
            missing = [field for field in required_fields if field not in column_names]
            if missing:
                logger.error(f"Still missing fields: {missing}")
                return False
            else:
                logger.info("✅ All required fields are present")
                return True
                
        except Exception as e:
            logger.error(f"Failed to verify migration: {e}")
            return False


async def test_insert():
    """测试插入数据"""
    logger.info("Testing data insertion...")
    
    async for session in get_db_session():
        try:
            # 测试插入一条净值数据
            test_sql = """
            INSERT INTO history_profits 
            (product_id, rip_cod, profit_date, unit_net_value, total_net_value, 
             net_value_change, show_provision, data_type, crawl_time)
            VALUES 
            ('test_product', 'TEST001', '2025-08-25', '1.0000', '1.0000', 
             '0.01', 0, 'net_value', datetime('now'))
            """
            
            await session.execute(text(test_sql))
            await session.commit()
            
            # 查询测试数据
            result = await session.execute(
                text("SELECT * FROM history_profits WHERE product_id = 'test_product'")
            )
            test_record = result.fetchone()
            
            if test_record:
                logger.info("✅ Test insertion successful")
                logger.info(f"Test record: {test_record}")
                
                # 删除测试数据
                await session.execute(
                    "DELETE FROM history_profits WHERE product_id = 'test_product'"
                )
                await session.commit()
                logger.info("Test record cleaned up")
                return True
            else:
                logger.error("❌ Test insertion failed - no record found")
                return False
                
        except Exception as e:
            await session.rollback()
            logger.error(f"❌ Test insertion failed: {e}")
            return False


async def main():
    """主函数"""
    print("数据库迁移脚本")
    print("=" * 50)
    
    try:
        # 初始化数据库
        logger.info("Initializing database...")
        await init_database()
        
        # 检查当前表结构
        missing_fields = await check_table_structure()
        
        if missing_fields:
            logger.info(f"Found {len(missing_fields)} missing fields")
            
            # 添加缺失的列
            success = await add_missing_columns(missing_fields)
            
            if success:
                # 验证迁移
                if await verify_migration():
                    # 测试插入
                    if await test_insert():
                        logger.info("🎉 Database migration completed successfully!")
                    else:
                        logger.error("❌ Migration verification failed")
                else:
                    logger.error("❌ Migration verification failed")
            else:
                logger.error("❌ Failed to add missing columns")
        else:
            logger.info("✅ Database schema is already up to date")
            
            # 仍然进行验证和测试
            if await verify_migration() and await test_insert():
                logger.info("✅ Database is ready for net value data")
    
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接
        await close_database()
        logger.info("Database connection closed")
    
    print("\n" + "=" * 50)
    print("迁移完成！")


if __name__ == "__main__":
    asyncio.run(main())
