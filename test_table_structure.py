#!/usr/bin/env python3
"""
测试新的表结构
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def main():
    try:
        print("🔧 测试新的表结构")
        print("=" * 50)
        
        # 导入必要的模块
        from src.nm_crawl.database.connection import init_database
        from sqlalchemy import inspect
        from src.nm_crawl.database.connection import get_db_session
        
        print("✅ 模块导入成功")
        
        # 初始化数据库
        print("📊 初始化数据库...")
        await init_database()
        print("✅ 数据库初始化成功")
        
        # 检查表结构
        print("🔍 检查表结构...")
        async for session in get_db_session():
            # 使用run_sync来执行同步的inspect操作
            def inspect_tables(conn):
                inspector = inspect(conn)
                tables = inspector.get_table_names()
                print(f"📋 数据库中的表: {tables}")

                # 检查每个表的列
                for table_name in tables:
                    print(f"\n📊 表 '{table_name}' 的列:")
                    columns = inspector.get_columns(table_name)
                    for col in columns:
                        print(f"   - {col['name']}: {col['type']}")

                return tables

            # 执行检查
            tables = await session.connection()
            await tables.run_sync(inspect_tables)
            break
        
        print("\n🎉 表结构检查完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
