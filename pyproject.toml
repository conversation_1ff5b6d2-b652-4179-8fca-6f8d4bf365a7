[project]
name = "apas_crawl"
version = "0.1.0"
description = "Bank financial product crawler using crawl4ai"
requires-python = ">=3.12"
dependencies = [
    "crawl4ai>=0.7.4",
    "sqlalchemy>=2.0.0",
    "aiosqlite>=0.19.0",
    "pydantic>=2.0.0",
    "asyncio",
    "aiofiles>=23.0.0",
    "schedule>=1.2.0",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.0",
    "click>=8.0.0",
    "httpx>=0.25.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0"
]

[project.scripts]
nm-crawl = "nm_crawl.main:cli"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/nm_crawl"]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.5.0"
]
