#!/usr/bin/env python3

print("Starting minimal test...")

try:
    import sys
    from pathlib import Path
    
    # 添加项目根目录到Python路径
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    print("Path setup complete")
    
    # 测试基本导入
    from sqlalchemy import Column, Integer, String
    print("SQLAlchemy import OK")
    
    from sqlalchemy.ext.declarative import declarative_base
    print("Declarative base import OK")
    
    # 测试我们的模型
    from src.nm_crawl.models.financial_models import Base
    print("Base model import OK")

    from src.nm_crawl.models.financial_models import ProductList
    print("ProductList import OK")
    
    print("✅ All imports successful!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
