#!/usr/bin/env python3
"""
Fixed crawler with proper JavaScript execution
"""

import asyncio
import json
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from crawl4ai import AsyncWebCrawler
from loguru import logger


async def fixed_crawl_list():
    """修复版本的列表爬取"""
    
    # 招商银行理财产品列表页URL
    list_page_url = (
        "https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html"
        "?_u=24&PrdTyp=A&behavior_entryid=LC0JGQ001&behavior_pageid=459C25E0"
        "&behavior_slotid=&appflag=1&_urlrefer=PLATFORM&DeviceType=E"
        "&Version=13.3.3&SystemVersion=11"
    )
    
    logger.info(f"开始修复版爬取: {list_page_url}")
    
    try:
        async with Async<PERSON>eb<PERSON>rawler(verbose=True) as crawler:
            # 配置浏览器选项
            browser_config = {
                "headless": False,  # 显示浏览器窗口
                "args": [
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-blink-features=AutomationControlled",
                    "--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                    "--window-size=414,896"  # iPhone尺寸
                ]
            }
            
            logger.info("第一步：基本页面加载...")
            
            # 第一步：基本加载页面，不执行JavaScript
            result1 = await crawler.arun(
                url=list_page_url,
                wait_for=5,
                browser_config=browser_config
            )
            
            logger.info(f"页面基本加载完成，HTML长度: {len(result1.html)}")
            
            # 第二步：等待更长时间让JavaScript加载完成
            logger.info("第二步：等待JavaScript加载完成...")
            
            js_code = """
            console.log('开始等待页面完全加载...');
            
            // 等待更长时间让Vue.js应用初始化
            await new Promise(resolve => setTimeout(resolve, 10000));
            
            console.log('检查页面状态...');
            
            // 检查是否有产品列表
            const productList = document.querySelector('.sa-list');
            const skeletonElements = document.querySelectorAll('.owl-skeleton');
            
            console.log('产品列表元素:', productList);
            console.log('骨架屏元素数量:', skeletonElements.length);
            
            // 如果还在显示骨架屏，尝试触发数据加载
            if (skeletonElements.length > 0) {
                console.log('检测到骨架屏，尝试触发数据加载...');
                
                // 尝试滚动触发
                window.scrollTo(0, 100);
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 尝试点击第一个标签
                const firstTab = document.querySelector('.swiper-slide');
                if (firstTab) {
                    firstTab.click();
                    console.log('点击了第一个标签');
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }
            }
            
            // 再次等待
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // 检查最终状态
            const finalProductList = document.querySelector('.sa-list');
            const finalProducts = finalProductList ? finalProductList.children.length : 0;
            
            console.log('最终产品数量:', finalProducts);
            
            // 尝试从页面中提取任何可能的产品信息
            const allText = document.body.innerText;
            const hasProductNames = allText.includes('理财') || allText.includes('收益') || allText.includes('%');
            
            console.log('页面包含产品相关文本:', hasProductNames);
            
            return {
                productCount: finalProducts,
                hasProductText: hasProductNames,
                pageText: allText.substring(0, 1000),  // 前1000个字符
                skeletonCount: document.querySelectorAll('.owl-skeleton').length
            };
            """
            
            result2 = await crawler.arun(
                url=list_page_url,
                wait_for=25,  # 增加等待时间
                browser_config=browser_config,
                js_code=js_code
            )
            
            logger.info("JavaScript执行完成")
            
            # 分析结果
            if hasattr(result2, 'js_execution_result') and result2.js_execution_result:
                js_result = result2.js_execution_result
                logger.info(f"JavaScript执行结果: {js_result}")
                
                # 保存最终的HTML
                os.makedirs("debug", exist_ok=True)
                final_html_file = f"debug/final_page_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
                with open(final_html_file, 'w', encoding='utf-8') as f:
                    f.write(result2.html)
                logger.info(f"最终页面HTML已保存到: {final_html_file}")
                
                # 尝试从HTML中直接提取产品信息
                products = extract_products_from_html(result2.html)
                
                if products:
                    logger.info(f"✓ 从HTML中提取到 {len(products)} 个产品")
                    for i, product in enumerate(products[:3], 1):
                        logger.info(f"  {i}. {product}")
                else:
                    logger.warning("✗ 没有从HTML中提取到产品信息")
                    
                    # 输出调试信息
                    logger.info("调试信息:")
                    logger.info(f"- 骨架屏数量: {js_result.get('skeletonCount', 0)}")
                    logger.info(f"- 包含产品文本: {js_result.get('hasProductText', False)}")
                    logger.info(f"- 页面文本预览: {js_result.get('pageText', '')[:200]}...")
                
                return products
            else:
                logger.error("JavaScript执行失败")
                return []
                
    except Exception as e:
        logger.error(f"爬取失败: {e}")
        import traceback
        traceback.print_exc()
        return []


def extract_products_from_html(html_content):
    """从HTML中提取产品信息"""
    products = []
    
    try:
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找产品列表
        product_lists = soup.find_all('ul', class_='sa-list')
        
        for ul in product_lists:
            items = ul.find_all('li')
            for item in items:
                # 尝试提取产品信息
                product_name = None
                product_rate = None
                
                # 查找产品名称
                name_elements = item.find_all(text=True)
                for text in name_elements:
                    text = text.strip()
                    if text and ('理财' in text or '宝' in text or '号' in text) and len(text) > 3:
                        product_name = text
                        break
                
                # 查找收益率
                for text in name_elements:
                    text = text.strip()
                    if '%' in text and any(c.isdigit() for c in text):
                        product_rate = text
                        break
                
                if product_name:
                    products.append({
                        'name': product_name,
                        'rate': product_rate
                    })
        
        # 如果没有找到，尝试从所有文本中提取
        if not products:
            all_text = soup.get_text()
            lines = all_text.split('\n')
            
            for line in lines:
                line = line.strip()
                if line and ('理财' in line or '宝' in line) and len(line) > 5 and len(line) < 50:
                    # 可能是产品名称
                    if not any(p['name'] == line for p in products):
                        products.append({
                            'name': line,
                            'rate': None
                        })
                        
                        if len(products) >= 10:  # 限制数量
                            break
        
    except Exception as e:
        logger.error(f"HTML解析失败: {e}")
    
    return products


async def main():
    """主函数"""
    logger.info("开始修复版爬虫测试")
    
    try:
        products = await fixed_crawl_list()
        
        if products:
            logger.info(f"✓ 成功获取到 {len(products)} 个产品")
            for i, product in enumerate(products[:5], 1):
                logger.info(f"  {i}. {product}")
        else:
            logger.warning("✗ 没有获取到任何产品")
            
            print("\n可能的解决方案:")
            print("1. 网站可能需要登录才能查看产品")
            print("2. 网站可能有反爬虫机制")
            print("3. 需要更长的等待时间让JavaScript加载")
            print("4. 可能需要模拟更真实的用户行为")
            print("5. 尝试使用不同的浏览器配置")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 设置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:HH:mm:ss} | {level} | {message}")
    
    # 运行测试
    asyncio.run(main())
