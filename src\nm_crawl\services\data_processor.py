"""
数据处理服务 - 从JSON文件读取数据并写入数据库
"""

import json
import os
import glob
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from loguru import logger

from .database_service import CmbDatabaseService
from ..database.connection import init_database


class DataProcessor:
    """数据处理器 - 处理爬取的JSON数据并写入数据库"""

    def __init__(self, data_directory: str = "data"):
        """
        初始化数据处理器
        
        Args:
            data_directory: 数据目录路径
        """
        self.data_directory = Path(data_directory)
        self.db_service = CmbDatabaseService()

        # 数据目录结构
        self.products_dir = self.data_directory / "products"
        self.details_dir = self.data_directory / "details"
        self.history_dir = self.data_directory / "history"

    async def process_all_data(self) -> Dict[str, int]:
        """
        处理所有数据文件并写入数据库
        
        Returns:
            Dict[str, int]: 处理结果统计
        """
        logger.info("开始处理所有数据文件...")

        # 初始化数据库
        await init_database()

        results = {
            "products": 0,
            "details": 0,
            "history": 0
        }

        # 处理产品列表数据
        products_count = await self._process_product_list_files()
        results["products"] = products_count

        # 处理产品详情数据
        details_count = await self._process_detail_files()
        results["details"] = details_count

        # 处理历史收益数据
        history_count = await self._process_history_files()
        results["history"] = history_count

        logger.info(f"数据处理完成: {results}")
        return results

    async def _process_product_list_files(self) -> int:
        """处理产品列表文件"""
        if not self.products_dir.exists():
            logger.warning(f"Products directory not found: {self.products_dir}")
            return 0

        total_count = 0

        # 查找所有产品列表JSON文件
        pattern = str(self.products_dir / "product_list_*.json")
        files = glob.glob(pattern)

        logger.info(f"Found {len(files)} product list files")

        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                products = data.get('products', [])
                if not products:
                    logger.warning(f"No products found in {file_path}")
                    continue

                # 获取爬取时间
                crawl_time_str = data.get('crawl_time')
                crawl_time = None
                if crawl_time_str:
                    try:
                        crawl_time = datetime.fromisoformat(crawl_time_str.replace('Z', '+00:00'))
                    except:
                        crawl_time = datetime.now()

                # 保存到数据库
                count = await self.db_service.save_product_list(products, crawl_time)
                total_count += count

                logger.info(f"Processed {count} products from {file_path}")

            except Exception as e:
                logger.error(f"Failed to process product list file {file_path}: {e}")

        return total_count

    async def _process_detail_files(self) -> int:
        """处理产品详情文件"""
        if not self.details_dir.exists():
            logger.warning(f"Details directory not found: {self.details_dir}")
            return 0

        total_count = 0

        # 查找所有详情JSON文件
        pattern = str(self.details_dir / "product_detail_*.json")
        files = glob.glob(pattern)

        logger.info(f"Found {len(files)} detail files")

        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 获取爬取时间
                crawl_time_str = data.get('crawl_time')
                crawl_time = None
                if crawl_time_str:
                    try:
                        crawl_time = datetime.fromisoformat(crawl_time_str.replace('Z', '+00:00'))
                    except:
                        crawl_time = datetime.now()

                # 详情数据可能在不同的键中
                detail_data = data.get('detail_data') or data.get('data') or data

                if detail_data:
                    success = await self.db_service.save_product_detail(detail_data, crawl_time)
                    if success:
                        total_count += 1
                        logger.info(f"Processed detail from {file_path}")
                else:
                    logger.warning(f"No detail data found in {file_path}")

            except Exception as e:
                logger.error(f"Failed to process detail file {file_path}: {e}")

        return total_count

    async def _process_history_files(self) -> int:
        """处理历史收益文件"""
        if not self.history_dir.exists():
            logger.warning(f"History directory not found: {self.history_dir}")
            return 0

        total_count = 0

        # 查找所有历史收益JSON文件
        pattern = str(self.history_dir / "history_*.json")
        files = glob.glob(pattern)

        logger.info(f"Found {len(files)} history files")

        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 从文件名中提取产品代码
                filename = Path(file_path).name
                rip_cod,chart_name = self._extract_rip_cod_from_filename(filename)

                if not rip_cod:
                    logger.warning(f"Could not extract ripCod from filename: {filename}")
                    continue

                try:
                    crawl_time = datetime.now()

                    saved_count = await self.db_service.save_chart_data(chart_name, rip_cod, data, crawl_time)
                    logger.info(f"Saved {saved_count} {chart_name} records to database for {rip_cod}")
                    total_count = saved_count
                except Exception as e:
                    logger.error(f"Failed to save {chart_name} data to database for {rip_cod}: {e}")

            except Exception as e:
                logger.error(f"Failed to process history file {file_path}: {e}")

        return total_count

    def _extract_rip_cod_from_filename(self, filename: str) -> Optional[tuple]:
        """从文件名中提取产品代码"""
        try:
            # 文件名格式: history_GY030111_get-history-profit_20250825_011405_287613.json
            parts = filename.split('_')
            if len(parts) > 3:
                return parts[1], parts[2]  # 返回两个值：get-history-profit 和 GY030111

            return None, None  # 如果无法提取，则返回两个None值
        except Exception as e:
            logger.error(f"Failed to extract ripCod from filename {filename}: {e}")
            return None, None  # 出错时也返回两个None值

    async def get_database_stats(self) -> Dict[str, int]:
        """获取数据库统计信息"""
        try:
            await init_database()

            stats = {
                "products": await self.db_service.get_product_count(),
                "history_records": await self.db_service.get_history_count()
            }

            return stats
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            return {"products": 0, "history_records": 0}
