#!/usr/bin/env python3
"""
Test script to verify the crawler fix
"""

import asyncio
import sys
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.crawlers.list_crawler import FinancialProductListCrawler


async def test_crawler_fix():
    """测试修复后的爬虫"""

    logger.info("开始测试修复后的爬虫...")

    try:
        # 创建爬虫实例，启用调试模式
        crawler = FinancialProductListCrawler(
            save_directory="test_data"
        )

        logger.info("爬虫实例创建成功")
        logger.info("注意：请查看浏览器控制台（F12 -> Console）来查看JavaScript日志")

        # 执行爬取，设置较短的等待时间进行测试
        products = await crawler.crawl_product_list(wait_time=30)

        logger.info(f"爬取完成，获得 {len(products)} 个产品")

        if products:
            logger.info("前3个产品示例:")
            for i, product in enumerate(products[:3]):
                logger.info(f"产品 {i+1}: {product.get('ripSnm', 'Unknown')} - {product.get('ripCod', 'Unknown')}")
        else:
            logger.warning("没有获取到产品数据，请检查:")
            logger.warning("1. 网络连接是否正常")
            logger.warning("2. 页面是否正确加载")
            logger.warning("3. 浏览器控制台是否有错误信息")

        return len(products) > 0

    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    logger.info("=== 爬虫修复测试 ===")
    
    success = await test_crawler_fix()
    
    if success:
        logger.info("✅ 测试成功！爬虫修复有效")
    else:
        logger.error("❌ 测试失败，需要进一步调试")
    
    return success


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
