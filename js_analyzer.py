#!/usr/bin/env python3
"""
JavaScript analyzer to find the real API endpoints
"""

import asyncio
import json
import sys
import os
import re
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import httpx
from loguru import logger


async def download_and_analyze_js():
    """下载并分析JavaScript文件"""
    
    # 从之前的分析中找到的JS文件
    js_files = [
        "https://img2.mb.cmbimg.com/static/ientrustfinance/financeproduct/js/financelist.6212e2a6.min.js",
        "https://img2.mb.cmbimg.com/static/ientrustfinance/financeproduct/js/chunk-vendors.981a5e0c.min.js",
        "https://img2.mb.cmbimg.com/static/ientrustfinance/financeproduct/js/chunk-ui.d495920e.min.js"
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Referer': 'https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html'
    }
    
    os.makedirs("debug/js", exist_ok=True)
    
    async with httpx.AsyncClient(timeout=30) as client:
        for js_url in js_files:
            try:
                logger.info(f"下载JS文件: {js_url}")
                response = await client.get(js_url, headers=headers)
                
                if response.status_code == 200:
                    js_content = response.text
                    logger.info(f"JS文件大小: {len(js_content)} 字符")
                    
                    # 保存JS文件
                    filename = js_url.split('/')[-1]
                    js_file_path = f"debug/js/{filename}"
                    with open(js_file_path, 'w', encoding='utf-8') as f:
                        f.write(js_content)
                    
                    # 分析JS文件中的API调用
                    apis = analyze_js_content(js_content, filename)
                    
                    if apis:
                        logger.info(f"在 {filename} 中找到 {len(apis)} 个API:")
                        for api in apis:
                            logger.info(f"  - {api}")
                        
                        # 尝试这些API
                        for api in apis:
                            products = await try_discovered_api(api)
                            if products:
                                return products, api
                
            except Exception as e:
                logger.error(f"处理JS文件失败 {js_url}: {e}")
    
    return [], None


def analyze_js_content(js_content, filename):
    """分析JavaScript内容找到API端点"""
    apis = set()
    
    # 各种API模式
    patterns = [
        # 完整URL
        r'https?://[^"\']+/[^"\']*(?:cash|finance|product|list)[^"\']*',
        # 相对路径
        r'["\'][/]?(?:api/)?(?:ientrustfinance/)?[^"\']*(?:cash|finance|product|list)[^"\']*["\']',
        # 方法调用中的路径
        r'(?:get|post|request)\s*\(\s*["\']([^"\']+)["\']',
        # axios或fetch调用
        r'(?:axios|fetch)\s*\.\s*(?:get|post)\s*\(\s*["\']([^"\']+)["\']',
        # URL拼接
        r'["\']([^"\']*(?:cash|finance|product|list)[^"\']*)["\']',
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, js_content, re.IGNORECASE)
        for match in matches:
            if isinstance(match, tuple):
                match = match[0] if match[0] else match[1]
            
            # 清理和验证
            match = match.strip('\'"')
            if match and ('cash' in match.lower() or 'finance' in match.lower() or 
                         'product' in match.lower() or 'list' in match.lower()):
                
                # 构建完整URL
                if match.startswith('http'):
                    apis.add(match)
                elif match.startswith('/'):
                    apis.add(f"https://mobile.cmbchina.com{match}")
                else:
                    apis.add(f"https://mobile.cmbchina.com/{match}")
    
    # 特殊模式：查找配置对象
    config_patterns = [
        r'baseURL\s*:\s*["\']([^"\']+)["\']',
        r'apiUrl\s*:\s*["\']([^"\']+)["\']',
        r'API_BASE\s*[=:]\s*["\']([^"\']+)["\']',
    ]
    
    for pattern in config_patterns:
        matches = re.findall(pattern, js_content, re.IGNORECASE)
        for match in matches:
            if match:
                logger.info(f"找到API配置: {match}")
    
    # 查找具体的端点名称
    endpoint_patterns = [
        r'["\']([^"\']*cashlistnew[^"\']*)["\']',
        r'["\']([^"\']*financelist[^"\']*)["\']',
        r'["\']([^"\']*productlist[^"\']*)["\']',
    ]
    
    for pattern in endpoint_patterns:
        matches = re.findall(pattern, js_content, re.IGNORECASE)
        for match in matches:
            if match and not match.startswith('http'):
                # 尝试不同的基础URL
                base_urls = [
                    "https://mobile.cmbchina.com",
                    "https://mobile.cmbchina.com/ientrustfinance",
                    "https://mobile.cmbchina.com/api"
                ]
                
                for base_url in base_urls:
                    if match.startswith('/'):
                        apis.add(f"{base_url}{match}")
                    else:
                        apis.add(f"{base_url}/{match}")
    
    return list(apis)


async def try_discovered_api(api_url):
    """尝试发现的API"""
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Referer': 'https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html',
        'X-Requested-With': 'XMLHttpRequest'
    }
    
    # 不同的参数组合
    param_sets = [
        {},
        {"PrdTyp": "A"},
        {"type": "A"},
        {"category": "A"},
        {"PrdTyp": "A", "pageSize": "20", "pageNum": "1"},
        {"PrdTyp": "A", "pageSize": "10", "pageNum": "0"},
    ]
    
    logger.info(f"尝试API: {api_url}")
    
    async with httpx.AsyncClient(timeout=30) as client:
        for params in param_sets:
            try:
                response = await client.get(api_url, params=params, headers=headers)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        # 检查是否是成功响应
                        if data.get('sysCode') == 200 or data.get('code') == 200 or data.get('success'):
                            logger.info(f"  ✓ 成功响应: {params}")
                            
                            # 分析数据
                            products = analyze_response_for_products(data)
                            if products:
                                logger.info(f"  ✓ 找到 {len(products)} 个产品!")
                                
                                # 保存成功的响应
                                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                success_file = f"debug/success_response_{timestamp}.json"
                                with open(success_file, 'w', encoding='utf-8') as f:
                                    json.dump({
                                        "api_url": api_url,
                                        "params": params,
                                        "response": data
                                    }, f, ensure_ascii=False, indent=2)
                                
                                return products
                        else:
                            logger.debug(f"  响应码: {data.get('sysCode', data.get('code', 'unknown'))}")
                    
                    except json.JSONDecodeError:
                        logger.debug(f"  非JSON响应: {response.text[:100]}...")
                
            except Exception as e:
                logger.debug(f"  请求失败: {e}")
    
    return []


def analyze_response_for_products(data):
    """分析响应数据寻找产品"""
    
    # 递归查找产品数据
    def find_product_list(obj, path=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                
                if isinstance(value, list) and len(value) > 0:
                    # 检查是否是产品列表
                    first_item = value[0]
                    if isinstance(first_item, dict):
                        # 查找产品相关字段
                        product_fields = ['ripSnm', 'ripCod', 'prdRat', 'name', 'title', 'rate']
                        if any(field in first_item for field in product_fields):
                            logger.info(f"  在 {current_path} 找到产品列表")
                            return value
                
                # 递归搜索
                result = find_product_list(value, current_path)
                if result:
                    return result
        
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                result = find_product_list(item, f"{path}[{i}]")
                if result:
                    return result
        
        return None
    
    return find_product_list(data)


async def main():
    """主函数"""
    logger.info("开始JavaScript分析")
    
    try:
        products, successful_api = await download_and_analyze_js()
        
        if products:
            logger.info(f"✓ 成功找到 {len(products)} 个产品!")
            logger.info(f"成功的API: {successful_api}")
            
            # 显示产品信息
            for i, product in enumerate(products[:5], 1):
                name = product.get('ripSnm') or product.get('name', '未知产品')
                rate = product.get('prdRat') or product.get('rate', 'N/A')
                code = product.get('ripCod') or product.get('code', 'N/A')
                logger.info(f"  {i}. {name} - {rate} (代码: {code})")
        else:
            logger.warning("✗ 没有找到产品数据")
            
            print("\n建议:")
            print("1. 检查debug/js/目录下的JavaScript文件")
            print("2. 手动分析JavaScript代码找到API调用")
            print("3. 可能需要POST请求而不是GET")
            print("4. 可能需要特殊的请求头或认证")
        
    except Exception as e:
        logger.error(f"分析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 设置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:HH:mm:ss} | {level} | {message}")
    
    # 运行分析
    asyncio.run(main())
