"""
Logging configuration and utilities
"""

import os
import sys
from typing import Optional, Dict, Any
from datetime import datetime
from loguru import logger
import json


class LoggerConfig:
    """日志配置类"""
    
    def __init__(self, log_directory: str = "logs"):
        """
        初始化日志配置
        
        Args:
            log_directory: 日志目录
        """
        self.log_directory = log_directory
        self.ensure_log_directory()
        
    def ensure_log_directory(self):
        """确保日志目录存在"""
        os.makedirs(self.log_directory, exist_ok=True)
        os.makedirs(f"{self.log_directory}/crawl", exist_ok=True)
        os.makedirs(f"{self.log_directory}/error", exist_ok=True)
        os.makedirs(f"{self.log_directory}/debug", exist_ok=True)
    
    def setup_logger(self, level: str = "INFO", enable_file_logging: bool = True):
        """
        设置日志配置
        
        Args:
            level: 日志级别
            enable_file_logging: 是否启用文件日志
        """
        # 移除默认的处理器
        logger.remove()
        
        # 控制台输出格式
        console_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
        
        # 添加控制台处理器
        logger.add(
            sys.stdout,
            format=console_format,
            level=level,
            colorize=True
        )
        
        if enable_file_logging:
            # 文件输出格式
            file_format = (
                "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | "
                "{name}:{function}:{line} | {message}"
            )
            
            # 添加通用日志文件处理器
            logger.add(
                f"{self.log_directory}/app.log",
                format=file_format,
                level=level,
                rotation="10 MB",
                retention="30 days",
                compression="zip",
                encoding="utf-8"
            )
            
            # 添加爬取专用日志处理器
            logger.add(
                f"{self.log_directory}/crawl/crawl.log",
                format=file_format,
                level="INFO",
                rotation="5 MB",
                retention="15 days",
                compression="zip",
                encoding="utf-8",
                filter=lambda record: "crawl" in record["name"].lower() or "crawler" in record["name"].lower()
            )
            
            # 添加错误日志处理器
            logger.add(
                f"{self.log_directory}/error/error.log",
                format=file_format,
                level="ERROR",
                rotation="5 MB",
                retention="60 days",
                compression="zip",
                encoding="utf-8"
            )
            
            # 添加调试日志处理器
            logger.add(
                f"{self.log_directory}/debug/debug.log",
                format=file_format,
                level="DEBUG",
                rotation="20 MB",
                retention="7 days",
                compression="zip",
                encoding="utf-8"
            )
        
        logger.info("Logger configured successfully")


class CrawlMonitor:
    """爬取监控器"""
    
    def __init__(self, log_directory: str = "logs"):
        """
        初始化监控器
        
        Args:
            log_directory: 日志目录
        """
        self.log_directory = log_directory
        self.metrics = {
            "crawl_sessions": 0,
            "successful_crawls": 0,
            "failed_crawls": 0,
            "total_products": 0,
            "total_details": 0,
            "total_history_records": 0,
            "last_crawl_time": None,
            "errors": [],
            "warnings": []
        }
        
        # 确保监控日志目录存在
        os.makedirs(f"{log_directory}/monitor", exist_ok=True)
        
        logger.info("CrawlMonitor initialized")
    
    def start_crawl_session(self, session_id: str, task_type: str):
        """
        开始爬取会话
        
        Args:
            session_id: 会话ID
            task_type: 任务类型
        """
        self.metrics["crawl_sessions"] += 1
        
        session_info = {
            "session_id": session_id,
            "task_type": task_type,
            "start_time": datetime.now().isoformat(),
            "status": "started"
        }
        
        self._log_session_event(session_info)
        logger.info(f"Crawl session started: {session_id} ({task_type})")
    
    def end_crawl_session(self, session_id: str, success: bool, result: Dict[str, Any] = None, error: str = None):
        """
        结束爬取会话
        
        Args:
            session_id: 会话ID
            success: 是否成功
            result: 结果数据
            error: 错误信息
        """
        if success:
            self.metrics["successful_crawls"] += 1
        else:
            self.metrics["failed_crawls"] += 1
            if error:
                self.metrics["errors"].append({
                    "session_id": session_id,
                    "error": error,
                    "timestamp": datetime.now().isoformat()
                })
        
        self.metrics["last_crawl_time"] = datetime.now().isoformat()
        
        # 更新统计数据
        if result:
            self.metrics["total_products"] += result.get("products_count", 0)
            self.metrics["total_details"] += result.get("details_count", 0)
            self.metrics["total_history_records"] += result.get("history_count", 0)
        
        session_info = {
            "session_id": session_id,
            "end_time": datetime.now().isoformat(),
            "status": "completed" if success else "failed",
            "result": result,
            "error": error
        }
        
        self._log_session_event(session_info)
        
        if success:
            logger.info(f"Crawl session completed successfully: {session_id}")
        else:
            logger.error(f"Crawl session failed: {session_id} - {error}")
    
    def log_warning(self, session_id: str, warning: str):
        """
        记录警告
        
        Args:
            session_id: 会话ID
            warning: 警告信息
        """
        warning_info = {
            "session_id": session_id,
            "warning": warning,
            "timestamp": datetime.now().isoformat()
        }
        
        self.metrics["warnings"].append(warning_info)
        self._log_session_event(warning_info)
        logger.warning(f"Session {session_id}: {warning}")
    
    def log_progress(self, session_id: str, progress: Dict[str, Any]):
        """
        记录进度
        
        Args:
            session_id: 会话ID
            progress: 进度信息
        """
        progress_info = {
            "session_id": session_id,
            "progress": progress,
            "timestamp": datetime.now().isoformat()
        }
        
        self._log_session_event(progress_info)
        logger.info(f"Session {session_id} progress: {progress}")
    
    def _log_session_event(self, event: Dict[str, Any]):
        """
        记录会话事件到文件
        
        Args:
            event: 事件信息
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d")
            log_file = f"{self.log_directory}/monitor/session_{timestamp}.jsonl"
            
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(event, ensure_ascii=False) + '\n')
                
        except Exception as e:
            logger.error(f"Failed to log session event: {e}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        获取监控指标
        
        Returns:
            Dict[str, Any]: 监控指标
        """
        # 计算成功率
        total_crawls = self.metrics["successful_crawls"] + self.metrics["failed_crawls"]
        success_rate = (self.metrics["successful_crawls"] / total_crawls * 100) if total_crawls > 0 else 0
        
        # 限制错误和警告数量（只保留最近的）
        recent_errors = self.metrics["errors"][-10:] if len(self.metrics["errors"]) > 10 else self.metrics["errors"]
        recent_warnings = self.metrics["warnings"][-10:] if len(self.metrics["warnings"]) > 10 else self.metrics["warnings"]
        
        return {
            **self.metrics,
            "success_rate": round(success_rate, 2),
            "total_crawls": total_crawls,
            "recent_errors": recent_errors,
            "recent_warnings": recent_warnings,
            "metrics_updated_at": datetime.now().isoformat()
        }
    
    def save_metrics_report(self) -> str:
        """
        保存监控报告
        
        Returns:
            str: 报告文件路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"{self.log_directory}/monitor/metrics_report_{timestamp}.json"
        
        try:
            metrics = self.get_metrics()
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(metrics, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Metrics report saved: {report_file}")
            return report_file
            
        except Exception as e:
            logger.error(f"Failed to save metrics report: {e}")
            raise
    
    def reset_metrics(self):
        """重置监控指标"""
        self.metrics = {
            "crawl_sessions": 0,
            "successful_crawls": 0,
            "failed_crawls": 0,
            "total_products": 0,
            "total_details": 0,
            "total_history_records": 0,
            "last_crawl_time": None,
            "errors": [],
            "warnings": []
        }
        logger.info("Metrics reset")
    
    def check_health(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            Dict[str, Any]: 健康状态
        """
        metrics = self.get_metrics()
        
        # 检查最近是否有成功的爬取
        last_crawl_time = metrics.get("last_crawl_time")
        is_healthy = True
        issues = []
        
        if last_crawl_time:
            last_crawl = datetime.fromisoformat(last_crawl_time)
            time_since_last = datetime.now() - last_crawl
            
            # 如果超过24小时没有爬取，认为不健康
            if time_since_last.total_seconds() > 24 * 3600:
                is_healthy = False
                issues.append("No crawling activity in the last 24 hours")
        else:
            is_healthy = False
            issues.append("No crawling activity recorded")
        
        # 检查成功率
        if metrics["success_rate"] < 50:
            is_healthy = False
            issues.append(f"Low success rate: {metrics['success_rate']}%")
        
        # 检查最近的错误
        recent_errors = metrics.get("recent_errors", [])
        if len(recent_errors) > 5:
            is_healthy = False
            issues.append(f"Too many recent errors: {len(recent_errors)}")
        
        return {
            "is_healthy": is_healthy,
            "issues": issues,
            "metrics_summary": {
                "total_crawls": metrics["total_crawls"],
                "success_rate": metrics["success_rate"],
                "last_crawl_time": last_crawl_time,
                "recent_errors_count": len(recent_errors)
            },
            "check_time": datetime.now().isoformat()
        }


# 全局监控器实例
crawl_monitor = None


def get_monitor(log_directory: str = "logs") -> CrawlMonitor:
    """
    获取全局监控器实例
    
    Args:
        log_directory: 日志目录
        
    Returns:
        CrawlMonitor: 监控器实例
    """
    global crawl_monitor
    
    if crawl_monitor is None:
        crawl_monitor = CrawlMonitor(log_directory)
    
    return crawl_monitor


def setup_logging(log_directory: str = "logs", level: str = "INFO", enable_file_logging: bool = True):
    """
    设置日志系统
    
    Args:
        log_directory: 日志目录
        level: 日志级别
        enable_file_logging: 是否启用文件日志
    """
    config = LoggerConfig(log_directory)
    config.setup_logger(level, enable_file_logging)
    
    # 初始化监控器
    get_monitor(log_directory)
    
    logger.info("Logging system initialized")
