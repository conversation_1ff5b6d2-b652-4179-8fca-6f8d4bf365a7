#!/usr/bin/env python3
"""
Final solution based on JavaScript analysis
"""

import asyncio
import json
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import httpx
from loguru import logger


async def try_real_api_endpoints():
    """基于JavaScript分析尝试真实的API端点"""
    
    # 基于JavaScript分析的真实API端点
    api_endpoints = [
        # 基础路径 + 方法名
        "https://mobile.cmbchina.com/ientrustfinance/financelist/cashlistnew",
        "https://mobile.cmbchina.com/ientrustfinance/cashlistnew",
        "https://mobile.cmbchina.com/api/ientrustfinance/financelist/cashlistnew",
        "https://mobile.cmbchina.com/api/ientrustfinance/cashlistnew",
        
        # 其他可能的端点
        "https://mobile.cmbchina.com/ientrustfinance/financelist/queryAllList",
        "https://mobile.cmbchina.com/ientrustfinance/financelist/queryCashList",
        "https://mobile.cmbchina.com/api/ientrustfinance/financelist/queryAllList",
        "https://mobile.cmbchina.com/api/ientrustfinance/financelist/queryCashList",
    ]
    
    # 基于JavaScript分析的请求参数
    param_combinations = [
        # 基础参数
        {
            "prdTyp": "A",
            "timTmp": int(datetime.now().timestamp() * 1000)  # 时间戳
        },
        # 完整参数
        {
            "prdTyp": "A",
            "ccyNbr": "",
            "slfTag": "",
            "bblTyp": "",
            "evlLvl": "",
            "trnCst": "",
            "prdFrm": "",
            "buyNf": "",
            "hotFlg": "",
            "ngtMkt": "",
            "rseFrm": "",
            "timTmp": int(datetime.now().timestamp() * 1000),
            "yRipCod": "",
            "ySaaCod": "",
            "yDalCod": "",
            "yPagCnt": "",
            "prdInfoYS": []
        },
        # 简化参数
        {
            "prdTyp": "A",
            "pageSize": "20",
            "pageNum": "1",
            "timTmp": int(datetime.now().timestamp() * 1000)
        }
    ]
    
    # 模拟真实的请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Content-Type': 'application/json;charset=UTF-8',
        'Referer': 'https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html',
        'Origin': 'https://mobile.cmbchina.com',
        'X-Requested-With': 'XMLHttpRequest',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    
    logger.info("尝试基于JavaScript分析的真实API端点...")
    
    async with httpx.AsyncClient(timeout=30) as client:
        for api_url in api_endpoints:
            for params in param_combinations:
                try:
                    logger.info(f"尝试: {api_url}")
                    logger.info(f"参数: {params}")
                    
                    # 尝试POST请求（更常见于现代API）
                    response = await client.post(api_url, json=params, headers=headers)
                    
                    logger.info(f"POST响应状态: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            logger.info(f"响应数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                            
                            # 检查是否是成功响应
                            if isinstance(data, dict):
                                sys_code = data.get('sysCode')
                                biz_result = data.get('bizResult', {})
                                
                                logger.info(f"sysCode: {sys_code}")
                                if biz_result:
                                    logger.info(f"bizResult.code: {biz_result.get('code')}")
                                
                                # 如果是成功响应，分析数据
                                if sys_code == 200 and biz_result.get('code') == 200:
                                    biz_data = biz_result.get('data', {})
                                    prd_list = biz_data.get('prdList', [])
                                    
                                    if prd_list:
                                        logger.info(f"✓ 成功！找到 {len(prd_list)} 个产品")
                                        
                                        # 保存成功的响应
                                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                        success_file = f"debug/success_final_{timestamp}.json"
                                        os.makedirs("debug", exist_ok=True)
                                        
                                        with open(success_file, 'w', encoding='utf-8') as f:
                                            json.dump({
                                                "api_url": api_url,
                                                "method": "POST",
                                                "params": params,
                                                "response": data
                                            }, f, ensure_ascii=False, indent=2)
                                        
                                        # 显示前几个产品
                                        for i, product in enumerate(prd_list[:5], 1):
                                            name = product.get('ripSnm', '未知产品')
                                            rate = product.get('prdRat', 'N/A')
                                            code = product.get('ripCod', 'N/A')
                                            logger.info(f"  {i}. {name} - {rate} (代码: {code})")
                                        
                                        return prd_list, api_url, params
                                    else:
                                        logger.info("响应成功但没有产品数据")
                                else:
                                    logger.info(f"API返回错误: sysCode={sys_code}, bizCode={biz_result.get('code')}")
                                    if 'sysMsg' in data:
                                        logger.info(f"错误信息: {data['sysMsg']}")
                            
                        except json.JSONDecodeError:
                            logger.info(f"响应不是JSON格式: {response.text[:200]}...")
                    
                    # 如果POST失败，尝试GET
                    if response.status_code != 200:
                        get_response = await client.get(api_url, params=params, headers=headers)
                        logger.info(f"GET响应状态: {get_response.status_code}")
                        
                        if get_response.status_code == 200:
                            try:
                                data = get_response.json()
                                if isinstance(data, dict) and data.get('sysCode') == 200:
                                    biz_result = data.get('bizResult', {})
                                    if biz_result.get('code') == 200:
                                        prd_list = biz_result.get('data', {}).get('prdList', [])
                                        if prd_list:
                                            logger.info(f"✓ GET成功！找到 {len(prd_list)} 个产品")
                                            return prd_list, api_url, params
                            except json.JSONDecodeError:
                                pass
                    
                except Exception as e:
                    logger.debug(f"请求失败: {e}")
                    continue
    
    return [], None, None


async def create_working_crawler():
    """创建一个可工作的爬虫示例"""
    
    logger.info("基于分析结果创建可工作的爬虫...")
    
    # 尝试找到可工作的API
    products, successful_api, successful_params = await try_real_api_endpoints()
    
    if products:
        logger.info(f"✓ 找到可工作的API: {successful_api}")
        logger.info(f"✓ 成功参数: {successful_params}")
        
        # 创建可工作的爬虫代码
        working_crawler_code = f'''
"""
Working crawler based on successful API discovery
"""

import asyncio
import httpx
import json
from datetime import datetime

async def crawl_financial_products():
    """可工作的理财产品爬虫"""
    
    api_url = "{successful_api}"
    params = {json.dumps(successful_params, indent=4)}
    
    headers = {{
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Type': 'application/json;charset=UTF-8',
        'Referer': 'https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html',
        'Origin': 'https://mobile.cmbchina.com',
        'X-Requested-With': 'XMLHttpRequest'
    }}
    
    async with httpx.AsyncClient(timeout=30) as client:
        response = await client.post(api_url, json=params, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('sysCode') == 200:
                biz_result = data.get('bizResult', {{}})
                if biz_result.get('code') == 200:
                    prd_list = biz_result.get('data', {{}}).get('prdList', [])
                    return prd_list
    
    return []

# 使用示例
if __name__ == "__main__":
    products = asyncio.run(crawl_financial_products())
    print(f"获取到 {{len(products)}} 个产品")
    for i, product in enumerate(products[:3], 1):
        print(f"{{i}}. {{product.get('ripSnm', '未知')}} - {{product.get('prdRat', 'N/A')}}")
'''
        
        # 保存可工作的爬虫代码
        with open("working_crawler.py", 'w', encoding='utf-8') as f:
            f.write(working_crawler_code)
        
        logger.info("✓ 已创建 working_crawler.py")
        
        return True
    else:
        logger.warning("✗ 没有找到可工作的API端点")
        
        print("\n可能的原因:")
        print("1. API需要特殊的认证token")
        print("2. 需要先访问页面获取session")
        print("3. API参数格式不正确")
        print("4. 需要特殊的请求头")
        print("5. 网站有反爬虫保护")
        
        print("\n建议的解决方案:")
        print("1. 使用浏览器开发者工具手动分析网络请求")
        print("2. 尝试使用Selenium模拟真实浏览器行为")
        print("3. 分析页面的JavaScript代码找到完整的API调用逻辑")
        print("4. 考虑使用代理或更换IP")
        
        return False


async def main():
    """主函数"""
    logger.info("开始最终解决方案测试")
    
    try:
        success = await create_working_crawler()
        
        if success:
            logger.info("✓ 成功创建可工作的爬虫！")
            print("\n现在您可以:")
            print("1. 运行 python working_crawler.py 测试爬虫")
            print("2. 将working_crawler.py中的代码集成到您的项目中")
            print("3. 根据需要修改参数和处理逻辑")
        else:
            logger.warning("✗ 未能创建可工作的爬虫")
            print("\n您可以:")
            print("1. 检查debug/目录下的调试文件")
            print("2. 手动使用浏览器分析网络请求")
            print("3. 尝试不同的请求参数组合")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 设置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:HH:mm:ss} | {level} | {message}")
    
    # 运行最终测试
    asyncio.run(main())
