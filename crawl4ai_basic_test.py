#!/usr/bin/env python3
"""
Basic crawl4ai test
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from crawl4ai import AsyncWebCrawler


async def test_basic_crawl():
    """测试基本爬取功能"""

    print("开始基本crawl4ai测试...")

    crawler = None
    try:
        crawler = AsyncWebCrawler(verbose=True)
        await crawler.__aenter__()
        print("AsyncWebCrawler创建成功")

        # 测试简单页面
        test_url = "https://httpbin.org/json"

        print(f"测试URL: {test_url}")

        result = await crawler.arun(url=test_url)

        print(f"爬取完成")
        print(f"HTML长度: {len(result.html) if result.html else 0}")
        print(f"Markdown长度: {len(result.markdown) if result.markdown else 0}")

        if result.html:
            print("HTML内容预览:")
            print(result.html[:200])

        return True

    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if crawler:
            try:
                await crawler.__aexit__(None, None, None)
                print("AsyncWebCrawler已正确关闭")
            except Exception as e:
                print(f"关闭crawler时出错: {e}")

        # 给asyncio一些时间来清理资源
        await asyncio.sleep(0.1)


async def test_js_execution():
    """测试JavaScript执行"""
    
    print("\n开始JavaScript执行测试...")
    
    try:
        async with AsyncWebCrawler(verbose=True) as crawler:
            print("AsyncWebCrawler创建成功")
            
            # 测试JavaScript执行
            test_url = "https://httpbin.org/json"
            
            js_code = """
            console.log('JavaScript执行测试');
            const title = document.title || 'No title';
            const body = document.body ? document.body.innerText.substring(0, 100) : 'No body';
            
            return {
                title: title,
                bodyPreview: body,
                timestamp: new Date().toISOString()
            };
            """
            
            print(f"测试URL: {test_url}")
            
            result = await crawler.arun(
                url=test_url,
                js_code=js_code,
                wait_for=5
            )
            
            print(f"爬取完成")
            
            if hasattr(result, 'js_execution_result') and result.js_execution_result:
                print("JavaScript执行结果:")
                print(result.js_execution_result)
                return True
            else:
                print("没有JavaScript执行结果")
                return False
            
    except Exception as e:
        print(f"JavaScript测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("crawl4ai基本功能测试")
    print("=" * 50)
    
    # 测试1: 基本爬取
    success1 = await test_basic_crawl()
    
    # 测试2: JavaScript执行
    success2 = await test_js_execution()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"基本爬取: {'✓ 成功' if success1 else '✗ 失败'}")
    print(f"JavaScript执行: {'✓ 成功' if success2 else '✗ 失败'}")
    
    if success1 and success2:
        print("\n✓ crawl4ai基本功能正常，可以继续网络监听测试")
    else:
        print("\n✗ crawl4ai基本功能有问题，需要检查安装")


if __name__ == "__main__":
    asyncio.run(main())
