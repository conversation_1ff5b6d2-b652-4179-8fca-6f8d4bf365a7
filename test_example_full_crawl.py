#!/usr/bin/env python3
"""
测试example_full_crawl函数
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.main import CrawlerApp
from src.nm_crawl.utils.logger import setup_logging


async def test_example_full_crawl_logic():
    """测试example_full_crawl的逻辑（不实际爬取）"""
    print("=== 测试example_full_crawl逻辑 ===")

    try:
        # 使用与example_full_crawl相同的初始化方式
        app = CrawlerApp("config.json")
        await app.initialize()

        print("✅ 应用初始化成功")

        # 测试crawl_full方法的参数兼容性
        # 这里不实际调用，只是验证方法存在且参数正确
        crawl_full_method = getattr(app, 'crawl_full', None)
        if crawl_full_method:
            print("✅ crawl_full方法存在")
            
            # 检查方法签名
            import inspect
            sig = inspect.signature(crawl_full_method)
            params = list(sig.parameters.keys())
            print(f"✅ crawl_full参数: {params}")
            
            if 'max_products' in params:
                print("✅ max_products参数存在（与example_full_crawl兼容）")
            else:
                print("❌ max_products参数不存在")
                return False
        else:
            print("❌ crawl_full方法不存在")
            return False

        # 测试DataProcessor兼容性
        from src.nm_crawl.services.data_processor import DataProcessor
        processor = DataProcessor("data")
        
        print("✅ DataProcessor初始化成功")

        # 测试get_database_stats方法（example_full_crawl中使用）
        stats = await processor.get_database_stats()
        print(f"✅ get_database_stats成功: 产品 {stats['products']} 个，历史记录 {stats['history_records']} 条")

        # 测试process_all_data方法（example_full_crawl中使用）
        process_all_data_method = getattr(processor, 'process_all_data', None)
        if process_all_data_method:
            print("✅ process_all_data方法存在")
        else:
            print("❌ process_all_data方法不存在")
            return False

        await app.cleanup()
        print("✅ 应用清理成功")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_basic_usage_imports():
    """测试basic_usage.py的导入兼容性"""
    print("\n=== 测试basic_usage.py导入兼容性 ===")
    
    try:
        # 测试所有basic_usage.py中的导入
        from src.nm_crawl.main import CrawlerApp
        print("✅ CrawlerApp导入成功")
        
        from src.nm_crawl.config.settings import AppConfig
        print("✅ AppConfig导入成功")
        
        from src.nm_crawl.utils.logger import setup_logging
        print("✅ setup_logging导入成功")
        
        from src.nm_crawl.services.data_processor import DataProcessor
        print("✅ DataProcessor导入成功")
        
        from src.nm_crawl.config.settings import save_config
        print("✅ save_config导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("开始测试example_full_crawl兼容性")
    print("=" * 60)
    
    # 设置基本日志
    setup_logging(level="INFO")
    
    # 运行测试
    test1_result = await test_basic_usage_imports()
    test2_result = await test_example_full_crawl_logic()
    
    print("\n" + "=" * 60)
    
    if test1_result and test2_result:
        print("🎉 example_full_crawl兼容性测试全部通过！")
        print("\n✅ main.py已完全修复，与basic_usage.py完全兼容")
        print("\n📋 验证结果:")
        print("  ✅ 所有导入正常")
        print("  ✅ CrawlerApp类兼容")
        print("  ✅ crawl_full方法签名正确")
        print("  ✅ DataProcessor兼容")
        print("  ✅ 配置系统正常")
        
        print("\n🚀 现在可以安全使用:")
        print("  - python examples/basic_usage.py")
        print("  - 直接调用example_full_crawl()函数")
        print("  - 集成到其他项目中")
        
        print("\n💡 注意事项:")
        print("  - 实际爬取需要Chrome DevTools Protocol连接")
        print("  - 确保网络连接正常")
        print("  - 检查配置文件设置")
        
    else:
        print("❌ 部分测试失败")
    
    return test1_result and test2_result


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
