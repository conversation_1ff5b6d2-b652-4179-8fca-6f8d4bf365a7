"""
Tests for configuration management
"""

import pytest
import tempfile
import os
import json
from src.nm_crawl.config.settings import Config<PERSON>anager, AppConfig


class TestConfigManager:
    """测试配置管理器"""
    
    def test_load_default_config(self):
        """测试加载默认配置"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_file = f.name
        
        try:
            manager = ConfigManager(config_file)
            config = manager.load_config()
            
            assert isinstance(config, AppConfig)
            assert config.app_name == "nm_crawl"
            assert config.database.url.startswith("sqlite+aiosqlite://")
            assert config.crawler.list_wait_time == 20
            
        finally:
            if os.path.exists(config_file):
                os.unlink(config_file)
    
    def test_load_config_from_file(self):
        """测试从文件加载配置"""
        config_data = {
            "app_name": "test_app",
            "database": {
                "url": "sqlite+aiosqlite:///test.db"
            },
            "crawler": {
                "list_wait_time": 30
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            config_file = f.name
        
        try:
            manager = ConfigManager(config_file)
            config = manager.load_config()
            
            assert config.app_name == "test_app"
            assert config.database.url == "sqlite+aiosqlite:///test.db"
            assert config.crawler.list_wait_time == 30
            
        finally:
            if os.path.exists(config_file):
                os.unlink(config_file)
    
    def test_save_config(self):
        """测试保存配置"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_file = f.name
        
        try:
            manager = ConfigManager(config_file)
            config = AppConfig(app_name="test_save")
            
            saved_file = manager.save_config(config)
            assert saved_file == config_file
            assert os.path.exists(config_file)
            
            # 验证保存的内容
            with open(config_file, 'r') as f:
                saved_data = json.load(f)
            
            assert saved_data["app_name"] == "test_save"
            
        finally:
            if os.path.exists(config_file):
                os.unlink(config_file)
    
    def test_env_overrides(self):
        """测试环境变量覆盖"""
        # 设置环境变量
        os.environ['DATABASE_URL'] = 'postgresql://test'
        os.environ['LOG_LEVEL'] = 'DEBUG'
        os.environ['CRAWLER_HEADLESS'] = 'false'
        
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                config_file = f.name
            
            manager = ConfigManager(config_file)
            config = manager.load_config()
            
            assert config.database.url == 'postgresql://test'
            assert config.logging.level == 'DEBUG'
            assert config.crawler.headless == False
            
        finally:
            # 清理环境变量
            for key in ['DATABASE_URL', 'LOG_LEVEL', 'CRAWLER_HEADLESS']:
                if key in os.environ:
                    del os.environ[key]
            
            if os.path.exists(config_file):
                os.unlink(config_file)


class TestAppConfig:
    """测试应用配置"""
    
    def test_default_values(self):
        """测试默认值"""
        config = AppConfig()
        
        assert config.app_name == "nm_crawl"
        assert config.version == "0.1.0"
        assert config.debug == False
        assert config.database.echo == False
        assert config.crawler.list_wait_time == 20
        assert config.logging.level == "INFO"
    
    def test_validation(self):
        """测试配置验证"""
        # 测试有效配置
        config = AppConfig(
            crawler={
                "list_wait_time": 10,
                "max_detail_products": 100
            },
            logging={
                "level": "DEBUG"
            }
        )
        
        assert config.crawler.list_wait_time == 10
        assert config.crawler.max_detail_products == 100
        assert config.logging.level == "DEBUG"
        
        # 测试无效配置
        with pytest.raises(ValueError):
            AppConfig(crawler={"list_wait_time": 2})  # 小于最小值
        
        with pytest.raises(ValueError):
            AppConfig(crawler={"max_detail_products": 0})  # 小于最小值
        
        with pytest.raises(ValueError):
            AppConfig(logging={"level": "INVALID"})  # 无效日志级别
