#!/usr/bin/env python3
"""
Debug crawler with enhanced console logging
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode, BrowserConfig


async def debug_crawler_with_console_logs():
    """调试爬虫并显示控制台日志"""
    
    # 招商银行理财产品列表页URL
    list_page_url = (
        "https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html"
        "?_u=24&PrdTyp=A&behavior_entryid=LC0JGQ001&behavior_pageid=459C25E0"
        "&behavior_slotid=&appflag=1&_urlrefer=PLATFORM&DeviceType=E"
        "&Version=13.3.3&SystemVersion=11"
    )
    
    logger.info(f"开始调试爬取: {list_page_url}")
    
    # 确保debug目录存在
    os.makedirs("debug", exist_ok=True)
    
    try:
        # 配置浏览器
        browser_config = BrowserConfig(
            headless=False,  # 显示浏览器以便调试
            java_script_enabled=True,
            verbose=True,
            
            viewport_width=414,
            viewport_height=896,
            user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
            extra_args=[
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--enable-logging",
                "--log-level=0",
                "--v=1"
            ]
        )
        
        # 配置爬取参数
        crawler_config = CrawlerRunConfig(
            session_id=f"debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            cache_mode=CacheMode.BYPASS,
            page_timeout=120 * 1000,  # 2 minutes timeout
            delay_before_return_html=3.0,
            capture_network_requests=True,
            
            # 简化的等待条件，专注于调试
            wait_for="""js:() => {
                console.log('=== DEBUG: Wait condition check ===');
                
                // 添加时间戳
                const now = Date.now();
                if (!window.crawlStartTime) {
                    window.crawlStartTime = now;
                    console.log('DEBUG: Starting crawl at:', new Date(now).toISOString());
                }
                
                const elapsedSeconds = (now - window.crawlStartTime) / 1000;
                console.log('DEBUG: Elapsed time:', elapsedSeconds, 'seconds');
                
                // 检查产品列表数量
                const prdListElements = document.querySelectorAll('.prd-list');
                const prdListCount = prdListElements.length;
                console.log('DEBUG: Product list count:', prdListCount);
                
                // 检查页面内容
                const bodyText = document.body ? document.body.innerText : '';
                const hasFinanceText = bodyText.includes('理财');
                console.log('DEBUG: Has finance text:', hasFinanceText);
                
                // 检查是否有底部提示
                const bottomTips = document.querySelectorAll('.ef_bottom_tip');
                console.log('DEBUG: Bottom tips found:', bottomTips.length);
                
                bottomTips.forEach((tip, index) => {
                    const isVisible = tip.offsetParent !== null;
                    const text = tip.textContent.trim();
                    console.log(`DEBUG: Bottom tip ${index}: visible=${isVisible}, text="${text}"`);
                });
                
                // 检查swiper slides
                const swiperSlides = document.querySelectorAll('.swiper-slide');
                console.log('DEBUG: Swiper slides found:', swiperSlides.length);
                
                swiperSlides.forEach((slide, index) => {
                    const isHidden = slide.getAttribute('aria-hidden');
                    const classList = slide.className;
                    console.log(`DEBUG: Slide ${index}: aria-hidden=${isHidden}, classes="${classList}"`);
                });
                
                // 简单的停止条件：产品数量超过5个或者时间超过30秒
                const shouldStopByCount = prdListCount > 5;
                const shouldStopByTime = elapsedSeconds > 30;
                const shouldStop = shouldStopByCount || shouldStopByTime;
                
                console.log('DEBUG: Should stop:', shouldStop, 
                           '(by count:', shouldStopByCount, 
                           ', by time:', shouldStopByTime, ')');
                
                if (shouldStop) {
                    console.log('DEBUG: Stopping crawl now!');
                }
                
                return shouldStop;
            }""",
            
            # 添加一些用户模拟行为
            js_code="""
            console.log('DEBUG: Starting user simulation');
            
            // 等待页面加载
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 尝试滚动页面
            console.log('DEBUG: Scrolling page');
            window.scrollTo(0, document.body.scrollHeight);
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            console.log('DEBUG: User simulation completed');
            """,
            
            simulate_user=True,
        )
        
        async with AsyncWebCrawler(config=browser_config, verbose=True) as crawler:
            logger.info("开始执行爬取...")
            
            result = await crawler.arun(
                url=list_page_url,
                config=crawler_config,
            )
            
            logger.info("爬取完成，分析结果...")
            
            # 保存页面内容用于分析
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if result.html:
                html_file = f"debug/debug_page_{timestamp}.html"
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(result.html)
                logger.info(f"页面HTML已保存到: {html_file}")
            
            # 分析网络请求
            if hasattr(result, 'network_requests') and result.network_requests:
                logger.info(f"捕获了 {len(result.network_requests)} 个网络请求")
                
                # 保存网络请求
                import json
                requests_file = f"debug/network_requests_{timestamp}.json"
                with open(requests_file, 'w', encoding='utf-8') as f:
                    json.dump(result.network_requests, f, ensure_ascii=False, indent=2)
                logger.info(f"网络请求已保存到: {requests_file}")
                
                # 分析目标API请求
                target_apis = ['cashlistnew', 'bcdlist', 'financelist']
                for event in result.network_requests:
                    if event.get('event_type') == 'response':
                        url = event.get('url', '')
                        if any(api in url.lower() for api in target_apis):
                            logger.info(f"找到目标API响应: {url}")
                            status = event.get('status', 0)
                            logger.info(f"响应状态: {status}")
            else:
                logger.warning("没有捕获到网络请求")
            
            return True
            
    except Exception as e:
        logger.error(f"调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    logger.info("=== 爬虫调试（增强日志版本） ===")
    
    success = await debug_crawler_with_console_logs()
    
    if success:
        logger.info("✅ 调试完成")
    else:
        logger.error("❌ 调试失败")
    
    return success


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
