#!/usr/bin/env python3
"""
Quick start script for the financial product crawler
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.main import CrawlerApp
from src.nm_crawl.config.settings import ConfigManager
from src.nm_crawl.utils.logger import setup_logging


async def quick_start():
    """快速启动爬虫"""
    print("=" * 60)
    print("银行理财产品爬虫 - 快速启动")
    print("=" * 60)
    
    # 检查配置文件
    config_file = "config.json"
    if not os.path.exists(config_file):
        print("配置文件不存在，正在创建默认配置...")
        config_manager = ConfigManager(config_file)
        config_manager.create_default_config()
        config_manager.create_example_env()
        print(f"✓ 已创建配置文件: {config_file}")
        print(f"✓ 已创建环境变量示例: .env.example")
    
    # 初始化应用
    print("\n正在初始化应用...")
    app = CrawlerApp(config_file)
    await app.initialize()
    print("✓ 应用初始化完成")
    
    try:
        # 显示菜单
        while True:
            print("\n" + "=" * 40)
            print("请选择操作:")
            print("1. 爬取产品列表")
            print("2. 完整爬取（列表+详情，限制5个产品）")
            print("3. 查看应用状态")
            print("4. 启动调度器")
            print("5. 退出")
            print("=" * 40)
            
            choice = input("请输入选择 (1-5): ").strip()
            
            if choice == "1":
                await crawl_list_demo(app)
            elif choice == "2":
                await crawl_full_demo(app)
            elif choice == "3":
                show_status(app)
            elif choice == "4":
                await start_scheduler_demo(app)
            elif choice == "5":
                print("正在退出...")
                break
            else:
                print("无效选择，请重新输入")
    
    finally:
        await app.cleanup()
        print("✓ 应用已清理完成")


async def crawl_list_demo(app):
    """演示爬取产品列表"""
    print("\n开始爬取产品列表...")
    print("注意：这将打开浏览器窗口并自动操作，请不要手动干预")
    
    try:
        result = await app.crawl_list(wait_time=15, max_scroll_attempts=3)
        
        print(f"\n✓ 成功爬取 {result['products_count']} 个产品")
        
        if result['products']:
            print("\n前3个产品信息:")
            for i, product in enumerate(result['products'][:3], 1):
                print(f"  {i}. {product.get('ripSnm', '未知产品')}")
                print(f"     代码: {product.get('ripCod', 'N/A')}")
                print(f"     收益率: {product.get('prdRat', 'N/A')}")
                print(f"     风险等级: {product.get('zylTag', 'N/A')}")
        
        print(f"\n数据已保存到 data/ 目录")
        
    except Exception as e:
        print(f"✗ 爬取失败: {e}")


async def crawl_full_demo(app):
    """演示完整爬取"""
    print("\n开始完整爬取（列表+详情）...")
    print("注意：为了演示，限制只爬取5个产品的详情")
    print("这将需要较长时间，请耐心等待...")
    
    try:
        result = await app.crawl_full(max_products=5)
        
        print(f"\n✓ 完整爬取完成:")
        print(f"  产品列表: {result['products_count']} 个")
        print(f"  产品详情: {result['details_count']} 个")
        print(f"  历史收益: {result['history_count']} 个")
        
        storage_result = result.get('storage_result', {})
        if storage_result.get('success'):
            print(f"  ✓ 数据已保存到数据库")
            products_stats = storage_result.get('products', {})
            print(f"    - 产品数据: 成功 {products_stats.get('success', 0)}, 失败 {products_stats.get('failed', 0)}")
        
        print(f"\n数据文件保存在 data/ 目录")
        print(f"日志文件保存在 logs/ 目录")
        
    except Exception as e:
        print(f"✗ 爬取失败: {e}")


def show_status(app):
    """显示应用状态"""
    print("\n应用状态信息:")
    
    try:
        status = app.get_status()
        
        config = status['config']
        print(f"  数据库: {config['database_url']}")
        print(f"  数据目录: {config['data_directory']}")
        print(f"  调度器: {'启用' if config['scheduler_enabled'] else '禁用'}")
        print(f"  日志级别: {config['log_level']}")
        
        health = status['health']
        print(f"  健康状态: {'✓ 健康' if health['is_healthy'] else '✗ 不健康'}")
        
        if not health['is_healthy'] and health.get('issues'):
            print("  问题:")
            for issue in health['issues']:
                print(f"    - {issue}")
        
        metrics = status['metrics']
        print(f"\n监控指标:")
        print(f"  总爬取次数: {metrics.get('total_crawls', 0)}")
        print(f"  成功率: {metrics.get('success_rate', 0)}%")
        print(f"  总产品数: {metrics.get('total_products', 0)}")
        print(f"  最后爬取: {metrics.get('last_crawl_time', '从未')}")
        
        recent_errors = metrics.get('recent_errors', [])
        if recent_errors:
            print(f"  最近错误: {len(recent_errors)} 个")
        
    except Exception as e:
        print(f"✗ 获取状态失败: {e}")


async def start_scheduler_demo(app):
    """演示启动调度器"""
    print("\n调度器演示:")
    print("注意：这只是演示，实际的调度器需要在配置中启用")
    
    config = app.config
    if not config.scheduler.enable_scheduler:
        print("调度器在配置中被禁用")
        print("要启用调度器，请修改 config.json 文件:")
        print('  "scheduler": {')
        print('    "enable_scheduler": true,')
        print('    "full_crawl_interval": "daily",')
        print('    "auto_start": true')
        print('  }')
        return
    
    print("调度器配置:")
    print(f"  完整爬取间隔: {config.scheduler.full_crawl_interval}")
    print(f"  列表爬取间隔: {config.scheduler.list_crawl_interval}")
    print(f"  自动启动: {config.scheduler.auto_start}")
    
    print("\n要启动调度器，请使用命令:")
    print("  nm-crawl start-scheduler")
    print("\n或者运行:")
    print("  python -m src.nm_crawl.main start-scheduler")


def main():
    """主函数"""
    try:
        # 设置基本日志
        setup_logging(level="INFO", enable_file_logging=False)
        
        # 运行快速启动
        asyncio.run(quick_start())
        
    except KeyboardInterrupt:
        print("\n\n用户中断，正在退出...")
    except Exception as e:
        print(f"\n程序出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
