#!/usr/bin/env python3
"""
测试注释字段的数据库结构
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def main():
    try:
        print("🔧 测试注释字段的数据库结构")
        print("=" * 50)
        
        # 导入必要的模块
        from src.nm_crawl.database.connection import init_database
        from src.nm_crawl.services.data_processor import DataProcessor
        
        print("✅ 模块导入成功")
        
        # 初始化数据库
        print("📊 初始化数据库...")
        await init_database()
        print("✅ 数据库初始化成功")
        
        # 处理数据
        print("📁 处理数据到数据库...")
        processor = DataProcessor("data")
        results = await processor.process_all_data()
        
        print("✅ 数据处理完成:")
        print(f"   - 产品列表: {results['products']} 个")
        print(f"   - 产品详情: {results['details']} 个")
        print(f"   - 历史记录: {results['history']} 条")
        
        # 验证表结构
        print("🔍 验证表结构...")
        from sqlalchemy import inspect
        from src.nm_crawl.database.connection import get_db_session
        
        async for session in get_db_session():
            inspector = inspect(session.bind)
            
            # 检查product_details表的列
            columns = inspector.get_columns('product_details')
            print(f"✅ product_details表有 {len(columns)} 个字段:")
            
            active_fields = []
            for col in columns:
                active_fields.append(col['name'])
                print(f"   - {col['name']}: {col['type']}")
            
            print(f"\n📋 启用的字段总数: {len(active_fields)}")
            print("💤 注释的字段可在需要时启用")
            break
        
        print("\n🎉 测试完成！")
        print("\n💡 字段管理说明:")
        print("   - 当前启用: 20个核心字段")
        print("   - 注释保留: 30+个扩展字段")
        print("   - 按需启用: 参考 FIELD_MANAGEMENT_GUIDE.md")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
