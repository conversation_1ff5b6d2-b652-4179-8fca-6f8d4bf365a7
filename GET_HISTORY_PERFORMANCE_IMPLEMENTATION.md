# get-history-performance API 实现完成

## 🎉 实现总结

我已经成功为您的理财产品爬虫系统添加了对 `get-history-performance` API 的支持。现在系统可以自动处理您提供的历史业绩数据。

## 📊 支持的数据结构

您提供的API响应数据：
```json
{
    "sysCode": 200,
    "sysMsg": "Success",
    "bizResult": {
        "code": 200,
        "data": {
            "list": [
                {
                    "prfTyp": "A",
                    "timeInterval": "近1月",
                    "netValueChange": "0.23",
                    "yeaYld": "2.68"
                },
                {
                    "prfTyp": "B",
                    "timeInterval": "近3月",
                    "netValueChange": "0.75",
                    "yeaYld": "2.96"
                },
                {
                    "prfTyp": "J",
                    "timeInterval": "今年以来",
                    "netValueChange": "1.36",
                    "yeaYld": "3.33"
                },
                {
                    "prfTyp": "F",
                    "timeInterval": "成立以来",
                    "netValueChange": "1.36",
                    "yeaYld": "3.33"
                }
            ]
        }
    }
}
```

## 🔧 已完成的实现

### 1. 数据库模型 (`src/nm_crawl/models/financial_models.py`)

添加了 `HistoryPerformance` 表：
```python
class HistoryPerformance(Base):
    """理财产品历史业绩表"""
    __tablename__ = "history_performance"

    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(String(100), nullable=False, index=True)
    rip_cod = Column(String(50), nullable=False, index=True)
    
    # 业绩数据字段
    prf_typ = Column(String(10), nullable=False, comment="业绩类型 (A/B/J/F)")
    time_interval = Column(String(50), nullable=False, comment="时间区间")
    net_value_change = Column(String(20), comment="净值变化")
    yea_yld = Column(String(20), comment="年化收益率")
    
    # 原始数据和时间戳
    raw_data = Column(JSON, comment="完整原始数据")
    created_at = Column(DateTime, default=func.now())
    crawl_time = Column(DateTime)
    
    # 唯一约束：产品ID + 业绩类型 + 时间区间
    __table_args__ = (
        UniqueConstraint('product_id', 'prf_typ', 'time_interval', name='uk_product_performance'),
    )
```

### 2. 表格配置 (`src/nm_crawl/config/table_config.py`)

添加了业绩数据的配置：
```python
performance_config = TableConfig(
    api_name="get-history-performance",
    table_name="history_performance",
    model_class=HistoryPerformance,
    unique_fields=["product_id", "prf_typ", "time_interval"],
    field_mappings=[
        FieldMapping("prfTyp", "prf_typ", required=True),
        FieldMapping("timeInterval", "time_interval", required=True),
        FieldMapping("netValueChange", "net_value_change"),
        FieldMapping("yeaYld", "yea_yld"),
    ],
    validator=validate_performance_data
)
```

### 3. 字段映射关系

| API字段 | 数据库字段 | 说明 |
|---------|------------|------|
| `prfTyp` | `prf_typ` | 业绩类型 (A/B/J/F) |
| `timeInterval` | `time_interval` | 时间区间 (近1月/近3月/今年以来/成立以来) |
| `netValueChange` | `net_value_change` | 净值变化 |
| `yeaYld` | `yea_yld` | 年化收益率 |

### 4. 爬虫集成 (`src/nm_crawl/crawlers/detail_crawler.py`)

在爬虫的 `chart_apis` 列表中添加了新的API：
```python
self.chart_apis = [
    "get-history-profit",      # 万份收益
    "get-history-net-value",   # 单位净值
    "get-history-performance", # 历史业绩 (新增)
]
```

### 5. 通用数据处理

通过通用表格服务 (`GenericTableService`)，系统会自动：
- 识别 `get-history-performance` API
- 提取 `bizResult.data.list` 中的数据
- 根据字段映射转换数据
- 验证数据完整性 (必填字段检查)
- 保存到 `history_performance` 表
- 处理数据冲突和去重

## 🚀 使用方法

### 自动处理 (推荐)

爬虫会自动识别并处理 `get-history-performance` API 的响应：

1. 当爬虫检测到URL中包含 `get-history-performance` 时
2. 自动提取响应数据中的 `bizResult.data.list`
3. 根据配置进行字段映射和验证
4. 保存到数据库，支持去重和更新

### 手动处理

如果需要手动处理数据：
```python
from src.nm_crawl.services.database_service import CmbDatabaseService

# 初始化数据库服务
db_service = CmbDatabaseService()

# 准备业绩数据 (从API响应中提取)
performance_data = api_response["bizResult"]["data"]["list"]

# 保存数据
saved_count = await db_service.save_chart_data(
    "get-history-performance", 
    "产品代码", 
    performance_data, 
    datetime.now()
)
```

## 🔍 数据验证

系统包含以下验证机制：
- **必填字段检查**: `prfTyp` 和 `timeInterval` 必须存在且非空
- **数据去重**: 基于 `(product_id, prf_typ, time_interval)` 组合去重
- **数据更新**: 相同组合的记录会被更新而不是重复插入
- **原始数据保存**: 完整的API响应数据保存在 `raw_data` 字段中

## 📈 支持的业绩类型

根据您的数据，系统支持以下业绩类型：
- **A**: 近1月业绩
- **B**: 近3月业绩  
- **J**: 今年以来业绩
- **F**: 成立以来业绩

## 🎯 现在支持的所有API

系统现在支持以下三种图表数据API：
1. `get-history-profit` → `history_profits` 表 (万份收益)
2. `get-history-net-value` → `history_netvalue` 表 (单位净值)
3. `get-history-performance` → `history_performance` 表 (历史业绩) **[新增]**

## ✅ 测试验证

创建了以下测试文件来验证功能：
- `test_performance_api.py` - 完整的功能测试
- `demo_performance_usage.py` - 使用演示
- `test_simple_performance.py` - 基础配置测试

## 🔧 技术特性

- **配置化**: 通过配置文件管理，易于维护和扩展
- **通用性**: 使用通用表格服务，代码复用性高
- **自动化**: 爬虫自动识别和处理，无需手动干预
- **数据完整性**: 支持数据验证、去重和更新
- **错误处理**: 完善的错误处理和日志记录
- **扩展性**: 易于添加新的API类型

## 🎉 总结

✅ **已完成**: `get-history-performance` API 的完整支持  
✅ **数据结构**: 完全匹配您提供的API响应格式  
✅ **自动处理**: 爬虫会自动识别并保存业绩数据  
✅ **数据质量**: 包含验证、去重和更新机制  
✅ **易于使用**: 无需修改爬虫核心逻辑  

现在您的爬虫系统可以完整处理理财产品的历史收益、历史净值和历史业绩三种类型的数据了！
