# 数据库表结构优化总结

## 🎯 优化目标

根据您的要求，我们已经完成了以下优化：

1. **去掉表名的cmb_前缀**
2. **精简详情表的字段数量**

## 📊 新的表结构

### 1. `product_list` - 产品列表表
- **原表名**: `cmb_product_list` → **新表名**: `product_list`
- **主键**: `product_id` (格式: `cmb_` + `ripCod`)
- **字段**: 保持原有字段不变，包含产品基本信息

### 2. `product_details` - 产品详情表 (精简版)
- **原表名**: `cmb_product_details` → **新表名**: `product_details`
- **主键**: `product_id` (格式: `cmb_` + `ripCod`)
- **字段优化**: 从原来的50+字段精简到20个核心字段
- **字段处理**: 非核心字段采用注释方式保留，便于后续按需启用

#### 精简后的核心字段：
```sql
-- 基础信息
product_id VARCHAR(100) PRIMARY KEY  -- 产品ID (cmb_+ripCod)
rip_cod VARCHAR(50) NOT NULL         -- 产品代码
saa_cod VARCHAR(50)                  -- 销售代码
rip_snm VARCHAR(200) NOT NULL        -- 产品名称

-- 发行机构信息
crp_nam VARCHAR(200)                 -- 发行机构名称
crp_cod VARCHAR(50)                  -- 发行机构代码

-- 收益率信息
rate_text VARCHAR(100)               -- 收益率显示文本
rate_name VARCHAR(50)                -- 收益率名称

-- 产品基本信息
ter_day VARCHAR(200)                 -- 投资期限描述
risk_lvl VARCHAR(50)                 -- 风险等级
sbs_uqt VARCHAR(100)                 -- 起购金额
invest_typ VARCHAR(50)               -- 投资类型

-- 交易规则（核心）
buy_time_rule TEXT                   -- 销售时间规则
pay_time_rule TEXT                   -- 到账时间规则

-- 其他重要信息
buy_sum VARCHAR(50)                  -- 购买人数
csh_prf VARCHAR(200)                 -- 现金收益描述

-- 产品特色
features JSON                        -- 产品特色列表

-- 完整原始数据
raw_data JSON                        -- 完整原始数据
crawl_time DATETIME                  -- 爬取时间
created_at DATETIME                  -- 创建时间
updated_at DATETIME                  -- 更新时间
```

### 3. `product_history` - 历史收益表
- **原表名**: `cmb_product_history` → **新表名**: `product_history`
- **字段**: 保持不变

## 🔧 已更新的文件

### 模型文件
- `src/nm_crawl/models/cmb_models.py`
  - 类名: `CmbProductDetail` → `ProductDetail`
  - 类名: `CmbProductHistory` → `ProductHistory`
  - 类名: `CmbProductList` → `ProductList`
  - 表名: 去掉 `cmb_` 前缀
  - 精简 `ProductDetail` 字段

### 服务文件
- `src/nm_crawl/services/database_service.py`
  - 更新导入和类引用
  - 更新详情数据保存逻辑，只保存核心字段

### 查询文件
- `query_database.py`
  - 更新模型导入和查询语句

### 测试文件
- `test_full_workflow.py`
- 其他测试脚本

## 📈 优化效果

### 字段数量对比
- **详情表字段**: 50+ → 20 个核心字段
- **减少比例**: 约60%的字段减少

### 保留的核心信息
✅ **保留**:
- 产品基本信息（代码、名称、机构）
- 收益率信息
- 风险等级和起购金额
- 核心交易规则
- 产品特色
- 完整原始JSON数据

💤 **注释保留**（可按需启用）:
- 大量内部状态标志
- 冗余的交易规则字段
- 非核心的产品属性
- 调试和内部使用字段
- 产品编号、内部编号等扩展信息
- 详细的交易规则字段
- 时间线数据等复杂结构

## 🎯 使用方式

### 数据处理
```bash
# 处理数据到新的表结构
python process_data_to_db.py

# 查询新的表结构
python query_database.py
```

### 表名更新
- 旧表名: `cmb_product_list`, `cmb_product_details`, `cmb_product_history`
- 新表名: `product_list`, `product_details`, `product_history`

## 💡 优势

1. **简洁性**: 表名更简洁，去掉不必要的前缀
2. **高效性**: 详情表字段减少60%，提高查询和存储效率
3. **实用性**: 保留所有核心业务字段
4. **完整性**: 通过raw_data字段保留完整原始数据
5. **兼容性**: 主键格式保持不变，确保数据一致性
6. **可扩展性**: 注释字段可随时启用，无需重新设计表结构
7. **维护性**: 代码中保留字段定义，便于理解和维护

## 🚀 下一步

现在您可以：
1. 使用新的表结构进行数据查询和分析
2. 基于精简的字段开发业务逻辑
3. 在需要完整数据时查询raw_data字段
4. 享受更高效的数据库性能

**✅ 数据库表结构优化完成！**
