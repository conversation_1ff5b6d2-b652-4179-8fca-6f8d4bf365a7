#!/usr/bin/env python3
"""
处理净值数据文件并写入数据库
"""

import asyncio
import json
import sys
import glob
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.database.connection import init_database, close_database
from src.nm_crawl.services.database_service import CmbDatabaseService
from loguru import logger


def find_net_value_files(directory: str = "data") -> list:
    """
    查找目录下所有包含 get-history-net-value 的JSON文件

    Args:
        directory: 搜索目录

    Returns:
        list: 文件路径列表
    """
    logger.info(f"Searching for net value files in directory: {directory}")

    # 搜索模式：包含 get-history-net-value 的 JSON 文件
    patterns = [
        f"{directory}/**/*get-history-net-value*.json",
        f"{directory}/*get-history-net-value*.json"
    ]

    files = []
    for pattern in patterns:
        found_files = glob.glob(pattern, recursive=True)
        files.extend(found_files)

    # 去重并排序
    files = sorted(list(set(files)))

    logger.info(f"Found {len(files)} net value files:")
    for file in files:
        logger.info(f"  - {file}")

    return files


async def process_net_value_file(file_path: str):
    """
    处理单个净值数据文件
    
    Args:
        file_path: 文件路径
    """
    logger.info(f"Processing net value file: {file_path}")
    
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 从文件名中提取产品代码
        filename = Path(file_path).name
        # 文件名格式: history_profit_get-history-net-value_133030A_20250825_165906_764430.json
        parts = filename.split('_')
        rip_cod = None
        
        for i, part in enumerate(parts):
            if part == "get-history-net-value" and i + 1 < len(parts):
                rip_cod = parts[i + 1]
                break
        
        if not rip_cod:
            logger.error(f"Could not extract ripCod from filename: {filename}")
            return 0
        
        logger.info(f"Extracted product code: {rip_cod}")
        
        # 获取净值数据
        net_value_data = data.get('get-history-net-value', [])
        
        if not net_value_data:
            logger.warning(f"No net value data found in {file_path}")
            return 0
        
        logger.info(f"Found {len(net_value_data)} net value records")
        
        # 显示前几条数据
        logger.info("Sample data:")
        for i, record in enumerate(net_value_data[:3]):
            logger.info(f"  Record {i+1}: {record}")
        
        # 初始化数据库服务
        db_service = CmbDatabaseService()
        
        # 保存净值数据
        crawl_time = datetime.now()
        saved_count = await db_service.save_net_value_history(
            rip_cod,
            net_value_data,
            crawl_time
        )
        
        logger.info(f"Successfully saved {saved_count} net value records for product {rip_cod}")
        return saved_count
        
    except Exception as e:
        logger.error(f"Failed to process file {file_path}: {e}")
        import traceback
        traceback.print_exc()
        return 0


async def verify_data_in_database(rip_cod: str):
    """
    验证数据是否正确写入数据库
    
    Args:
        rip_cod: 产品代码
    """
    logger.info(f"Verifying data for product: {rip_cod}")
    
    try:
        from src.nm_crawl.database.connection import get_db_session
        from src.nm_crawl.models.financial_models import HistoryNetValue
        from sqlalchemy import select

        async for session in get_db_session():
            # 查询该产品的净值数据
            stmt = select(HistoryNetValue).where(
                HistoryNetValue.rip_cod == rip_cod
            ).order_by(HistoryNetValue.net_value_date.desc()).limit(5)

            result = await session.execute(stmt)
            records = result.scalars().all()

            logger.info(f"Found {len(records)} net value records in database")

            for i, record in enumerate(records):
                logger.info(f"Record {i+1}:")
                logger.info(f"  Date: {record.net_value_date}")
                logger.info(f"  Unit Net Value: {record.unit_net_value}")
                logger.info(f"  Total Net Value: {record.total_net_value}")
                logger.info(f"  Net Value Change: {record.net_value_change}")
                logger.info(f"  Show Provision: {record.show_provision}")

            # 统计总数
            count_stmt = select(HistoryNetValue).where(
                HistoryNetValue.rip_cod == rip_cod
            )
            count_result = await session.execute(count_stmt)
            total_count = len(count_result.scalars().all())

            logger.info(f"Total net value records for {rip_cod}: {total_count}")

            break
            
    except Exception as e:
        logger.error(f"Failed to verify data: {e}")
        import traceback
        traceback.print_exc()


async def process_all_net_value_files(directory: str = "data") -> dict:
    """
    处理目录下所有净值数据文件

    Args:
        directory: 搜索目录

    Returns:
        dict: 处理结果统计
    """
    # 查找所有净值文件
    files = find_net_value_files(directory)

    if not files:
        logger.warning(f"No net value files found in directory: {directory}")
        return {"total_files": 0, "processed_files": 0, "total_records": 0, "failed_files": []}

    # 统计信息
    stats = {
        "total_files": len(files),
        "processed_files": 0,
        "total_records": 0,
        "failed_files": [],
        "processed_products": set()
    }

    logger.info(f"Starting to process {len(files)} net value files...")

    # 处理每个文件
    for i, file_path in enumerate(files, 1):
        logger.info(f"\n[{i}/{len(files)}] Processing: {file_path}")

        try:
            saved_count = await process_net_value_file(file_path)

            if saved_count > 0:
                stats["processed_files"] += 1
                stats["total_records"] += saved_count

                # 从文件名提取产品代码
                filename = Path(file_path).name
                parts = filename.split('_')
                for j, part in enumerate(parts):
                    if part == "get-history-net-value" and j + 1 < len(parts):
                        rip_cod = parts[j + 1]
                        stats["processed_products"].add(rip_cod)
                        break

                logger.info(f"✅ Successfully processed {saved_count} records from {file_path}")
            else:
                stats["failed_files"].append(file_path)
                logger.warning(f"⚠️  No records saved from {file_path}")

        except Exception as e:
            stats["failed_files"].append(file_path)
            logger.error(f"❌ Failed to process {file_path}: {e}")

    return stats


async def main():
    """主函数"""
    logger.info("🏦 批量处理净值历史数据文件")
    logger.info("=" * 60)

    try:
        # 初始化数据库
        logger.info("📊 Initializing database...")
        await init_database()
        logger.info("✅ Database initialized successfully")

        # 处理所有净值文件
        stats = await process_all_net_value_files("data")

        # 显示处理结果
        logger.info("\n📈 处理结果统计:")
        logger.info(f"  总文件数: {stats['total_files']}")
        logger.info(f"  成功处理: {stats['processed_files']}")
        logger.info(f"  失败文件: {len(stats['failed_files'])}")
        logger.info(f"  总记录数: {stats['total_records']}")
        logger.info(f"  涉及产品: {len(stats['processed_products'])}")

        if stats['processed_products']:
            logger.info(f"  产品代码: {', '.join(sorted(stats['processed_products']))}")

        if stats['failed_files']:
            logger.warning("\n⚠️  失败的文件:")
            for failed_file in stats['failed_files']:
                logger.warning(f"    - {failed_file}")

        # 验证数据库中的数据
        if stats['processed_products']:
            logger.info("\n🔍 验证数据库数据...")
            db_service = CmbDatabaseService()

            try:
                net_value_count = await db_service.get_net_value_count()
                logger.info(f"✅ 数据库中净值记录总数: {net_value_count}")

                # 验证第一个产品的数据
                first_product = sorted(stats['processed_products'])[0]
                await verify_data_in_database(first_product)

            except Exception as e:
                logger.error(f"验证数据库数据失败: {e}")

        if stats['processed_files'] > 0:
            logger.info(f"\n✅ 成功处理 {stats['processed_files']} 个文件，共 {stats['total_records']} 条记录！")
        else:
            logger.warning("\n❌ 没有成功处理任何文件")

    except Exception as e:
        logger.error(f"❌ 批量处理失败: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 关闭数据库连接
        await close_database()
        logger.info("✅ Database connection closed")

    logger.info("\n💡 提示:")
    logger.info("- 数据库文件位置: data/financial_products.db")
    logger.info("- 净值表名: history_netvalue")
    logger.info("- 可以使用 python query_netvalue_database.py 查询数据")
    logger.info("- 可以使用 SQLite 工具直接查询数据库")


if __name__ == "__main__":
    asyncio.run(main())
