#!/usr/bin/env python3
"""
创建净值历史数据表
"""

import asyncio
import sys
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from src.nm_crawl.database.connection import init_database, close_database
from src.nm_crawl.models.financial_models import Base, HistoryNetValue
from sqlalchemy.ext.asyncio import create_async_engine
import os


async def create_netvalue_table():
    """创建净值历史数据表"""
    try:
        logger.info("Creating net value table...")
        
        # 数据库路径
        db_path = os.path.join(os.getcwd(), "data", "financial_products.db")
        database_url = f"sqlite+aiosqlite:///{db_path}"
        
        # 创建引擎
        engine = create_async_engine(database_url, echo=True)
        
        # 创建表
        async with engine.begin() as conn:
            # 只创建 HistoryNetValue 表
            await conn.run_sync(HistoryNetValue.__table__.create, checkfirst=True)
        
        logger.info("✅ Net value table created successfully")
        
        # 关闭引擎
        await engine.dispose()
        
    except Exception as e:
        logger.error(f"Failed to create net value table: {e}")
        import traceback
        traceback.print_exc()


async def verify_table_creation():
    """验证表是否创建成功"""
    try:
        from src.nm_crawl.database.connection import get_db_session
        from sqlalchemy import text
        
        # 初始化数据库连接
        await init_database()
        
        async for session in get_db_session():
            # 查询表是否存在
            result = await session.execute(
                text("SELECT name FROM sqlite_master WHERE type='table' AND name='history_netvalue';")
            )
            tables = result.fetchall()
            
            if tables:
                logger.info("✅ Table 'history_netvalue' exists")
                
                # 查询表结构
                result = await session.execute(text("PRAGMA table_info(history_netvalue);"))
                columns = result.fetchall()
                
                logger.info("Table structure:")
                for col in columns:
                    logger.info(f"  {col}")
                    
            else:
                logger.error("❌ Table 'history_netvalue' does not exist")
        
        await close_database()
        
    except Exception as e:
        logger.error(f"Failed to verify table: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    logger.info("🏦 Creating Net Value Table")
    logger.info("=" * 50)
    
    try:
        # 创建表
        await create_netvalue_table()
        
        # 验证表创建
        await verify_table_creation()
        
        logger.info("✅ Table creation completed!")
        
    except Exception as e:
        logger.error(f"Table creation failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 设置日志
    logger.remove()
    logger.add(
        sys.stdout, 
        level="INFO", 
        format="{time:HH:mm:ss} | {level} | {message}",
        colorize=True
    )
    
    # 运行创建
    asyncio.run(main())
