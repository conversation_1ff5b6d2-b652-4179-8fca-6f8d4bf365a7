"""
通用表格数据服务
支持根据配置动态处理不同类型的表格数据
"""

from typing import Dict, List, Any, Optional, Type
from datetime import datetime
from loguru import logger
from sqlalchemy import insert
from sqlalchemy.dialects.sqlite import insert as sqlite_insert
from sqlalchemy.ext.asyncio import AsyncSession

from ..config.table_config import TableConfig, FieldMapping, get_table_config
from ..database.connection import get_db_session


class GenericTableService:
    """通用表格数据服务"""
    
    def __init__(self, bank_prefix: str = "cmb_"):
        self.bank_prefix = bank_prefix
    
    def _generate_product_id(self, rip_cod: str) -> str:
        """生成产品ID"""
        return f"{self.bank_prefix}{rip_cod}"
    
    def _transform_field_value(self, value: Any, mapping: FieldMapping) -> Any:
        """转换字段值"""
        # 如果有自定义转换函数，使用它
        if mapping.transformer:
            try:
                return mapping.transformer(value)
            except Exception as e:
                logger.warning(f"Field transformation failed for {mapping.source_field}: {e}")
                return mapping.default_value
        
        # 如果值为空，返回默认值
        if value is None or value == '':
            return mapping.default_value
        
        # 根据数据类型进行转换
        try:
            if mapping.data_type == "boolean":
                if isinstance(value, bool):
                    return value
                if isinstance(value, str):
                    return value.lower() in ('true', '1', 'yes', 'on')
                return bool(value)
            elif mapping.data_type == "integer":
                return int(float(value)) if value else mapping.default_value
            elif mapping.data_type == "float":
                return float(value) if value else mapping.default_value
            elif mapping.data_type == "json":
                return value  # JSON字段直接返回原值
            else:  # string
                return str(value) if value is not None else mapping.default_value
        except (ValueError, TypeError) as e:
            logger.warning(f"Type conversion failed for {mapping.source_field}: {e}")
            return mapping.default_value
    
    def _extract_data_from_item(self, item: Dict[str, Any], config: TableConfig) -> Dict[str, Any]:
        """从数据项中提取字段"""
        extracted = {}
        
        # 处理字段映射
        for mapping in config.field_mappings:
            source_value = item.get(mapping.source_field)
            transformed_value = self._transform_field_value(source_value, mapping)
            extracted[mapping.target_field] = transformed_value
        
        return extracted
    
    def _validate_data_item(self, item: Dict[str, Any], config: TableConfig) -> bool:
        """验证数据项"""
        # 使用自定义验证器
        if config.validator:
            try:
                return config.validator(item)
            except Exception as e:
                logger.warning(f"Custom validation failed: {e}")
                return False
        
        # 默认验证：检查必填字段
        for mapping in config.field_mappings:
            if mapping.required:
                value = item.get(mapping.source_field)
                if value is None or value == '':
                    logger.warning(f"Required field {mapping.source_field} is missing or empty")
                    return False
        
        return True
    
    async def save_table_data(
        self, 
        api_name: str, 
        rip_cod: str, 
        data_list: List[Dict[str, Any]], 
        crawl_time: datetime = None
    ) -> int:
        """
        保存表格数据的通用方法
        
        Args:
            api_name: API名称，如 "get-history-profit"
            rip_cod: 产品代码
            data_list: 数据列表
            crawl_time: 爬取时间
            
        Returns:
            int: 保存的记录数量
        """
        if not data_list or not rip_cod:
            return 0
        
        # 获取配置
        config = get_table_config(api_name)
        if not config:
            logger.error(f"No configuration found for API: {api_name}")
            return 0
        
        if crawl_time is None:
            crawl_time = datetime.now()
        
        product_id = self._generate_product_id(rip_cod)
        saved_count = 0
        
        async for session in get_db_session():
            try:
                for data_item in data_list:
                    # 验证数据
                    if not self._validate_data_item(data_item, config):
                        logger.warning(f"Data validation failed for item: {data_item}")
                        continue
                    
                    # 提取字段数据
                    extracted_data = self._extract_data_from_item(data_item, config)

                    # 添加公共字段
                    extracted_data.update({
                        'product_id': product_id,
                        'rip_cod': rip_cod,
                        'crawl_time': crawl_time
                    })

                    # 如果配置了raw_data字段，保存原始数据
                    if hasattr(config.model_class, 'raw_data'):
                        extracted_data['raw_data'] = data_item

                    # 构建插入语句 - 使用SQLite方言
                    stmt = sqlite_insert(config.model_class).values(**extracted_data)

                    # 处理冲突（去重）
                    if config.unique_fields:
                        update_dict = {}
                        for key in extracted_data.keys():
                            if key not in config.unique_fields:
                                update_dict[key] = extracted_data[key]

                        stmt = stmt.on_conflict_do_update(
                            index_elements=config.unique_fields,
                            set_=update_dict
                        )
                    
                    await session.execute(stmt)
                    saved_count += 1
                
                await session.commit()
                logger.info(f"Successfully saved {saved_count} {config.table_name} records for {rip_cod}")
                return saved_count
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Failed to save {config.table_name} data for {rip_cod}: {e}")
                raise
        
        return 0
    
    async def get_supported_apis(self) -> List[str]:
        """获取支持的API列表"""
        from ..config.table_config import table_config_manager
        return table_config_manager.get_api_names()
    
    async def is_api_supported(self, api_name: str) -> bool:
        """检查API是否支持"""
        from ..config.table_config import table_config_manager
        return table_config_manager.is_supported_api(api_name)
