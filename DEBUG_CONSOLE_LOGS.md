# 如何查看爬虫控制台日志

## 问题说明
你提到"console没日志"，这是因为JavaScript的`console.log`输出需要在浏览器的开发者工具中查看。

## 查看控制台日志的步骤

### 1. 运行调试脚本
```bash
python debug_crawler_with_logs.py
```

### 2. 打开浏览器开发者工具
当爬虫启动并打开浏览器窗口后：

1. **按F12键** 或者 **右键点击页面 -> 检查**
2. 点击 **Console（控制台）** 标签页
3. 你应该能看到以 `DEBUG:` 开头的日志信息

### 3. 查看日志内容
你应该能看到类似这样的日志：
```
DEBUG: Starting crawl at: 2024-01-XX...
DEBUG: Elapsed time: 1.234 seconds
DEBUG: Product list count: 5
DEBUG: Has finance text: true
DEBUG: Bottom tips found: 2
DEBUG: Should stop: false (by count: false, by time: false)
```

## 日志说明

### 关键日志信息
- `DEBUG: Product list count: X` - 当前页面找到的产品数量
- `DEBUG: Elapsed time: X seconds` - 已等待的时间
- `DEBUG: Should stop: true/false` - 是否满足停止条件
- `DEBUG: Bottom tips found: X` - 找到的底部提示数量

### 停止条件
爬虫会在以下情况停止：
1. 产品数量 > 5个 (调试版本降低了阈值)
2. 等待时间 > 30秒 (调试版本缩短了时间)

## 如果仍然看不到日志

### 方法1：检查控制台设置
1. 在控制台中，确保没有过滤器被启用
2. 检查日志级别设置，确保显示所有级别的日志

### 方法2：使用简化版本
运行更简单的测试：
```bash
python test_crawler_fix.py
```

### 方法3：手动检查
在浏览器控制台中手动输入：
```javascript
console.log('Test log message');
document.querySelectorAll('.prd-list').length;
```

## 常见问题

### Q: 浏览器窗口一闪而过
A: 这可能是因为爬虫很快就满足了停止条件。检查日志中的停止原因。

### Q: 页面加载很慢
A: 这是正常的，因为页面需要加载JavaScript和数据。等待时间已设置为最多2分钟。

### Q: 没有找到产品
A: 检查页面是否正确加载，以及CSS选择器`.prd-list`是否正确。

## 进一步调试

如果问题仍然存在，可以：

1. 检查 `debug/` 目录下生成的HTML文件
2. 查看 `debug/` 目录下的网络请求JSON文件
3. 在浏览器中手动访问URL，检查页面结构

## 联系支持

如果以上步骤都无法解决问题，请提供：
1. 控制台的完整日志输出
2. `debug/` 目录下的文件
3. 具体的错误信息
